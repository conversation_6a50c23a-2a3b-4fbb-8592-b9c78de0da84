<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    Checkstyle configuration that checks the Google coding conventions from Google Java Style
    that can be found at https://google.github.io/styleguide/javaguide.html

    Checkstyle is very configurable. Be sure to read the documentation at
    http://checkstyle.org (or in your downloaded distribution).

    To completely disable a check, just comment it out or delete it from the file.
    To suppress certain violations please review suppression filters.

    Authors: <AUTHORS>
 -->
<module name="Checker">
    <module name="SuppressWarningsFilter"/>

    <property name="charset" value="UTF-8"/>
    <property name="localeCountry" value="CN"/>
    <property name="localeLanguage" value="zh"/>

    <!--  默认严重级别为 warning-->
    <property name="severity" value="warning"/>

    <property name="fileExtensions" value="java, properties, xml"/>
    <!-- 排除所有 'module-info.java' 'package-info.java' 文件              -->
    <!-- See https://checkstyle.org/config_filefilters.html -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value="(package|module)\-info\.java$"/>
    </module>
    <!-- https://checkstyle.org/config_filters.html#SuppressionFilter -->
    <module name="SuppressionFilter">
        <property name="file" value="${org.checkstyle.google.suppressionfilter.config}"
                  default="checkstyle-suppressions.xml" />
        <property name="optional" value="true"/>
    </module>

    <!--  检查代码中是否出现了 tab 字符（'\t'），应该使用空格代替 tab-->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>
    <!--  检查单行代码长度-->
    <module name="LineLength">
        <property name="fileExtensions" value="java"/>
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
    </module>

    <module name="TreeWalker">
        <!--    检查类名与文件名一致-->
        <module name="OuterTypeFilename">
            <property name="severity" value="error"/>
        </module>
        <!--    检查是否出现了非法的字面量定义 -->
        <module name="IllegalTokenText">
            <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
            <property name="format"
                      value="\\u00(09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
            <property name="message"
                      value="请考虑使用特殊的转义序列，而不是八进制值或Unicode转义值。"/>
        </module>
        <!--    检查是否存在 unicode 字符的转义-->
        <module name="AvoidEscapedUnicodeCharacters">
            <property name="allowEscapesForControlCharacters" value="true"/>
            <property name="allowByTailComment" value="true"/>
            <property name="allowNonPrintableEscapes" value="true"/>
        </module>
        <!--    检查一个文件只有一个顶级类-->
        <module name="OneTopLevelClass">
            <property name="severity" value="error"/>
        </module>
        <!--    检查指定 token 不应该存在换行-->
        <module name="NoLineWrap">
            <property name="tokens" value="PACKAGE_DEF, IMPORT, STATIC_IMPORT"/>
        </module>
        <!--    检查空语句块-->
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens"
                      value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <!--    检查应该用括号括起来但是省略了括号的代码-->
        <module name="NeedBraces">
            <property name="tokens"
                      value="LITERAL_DO, LITERAL_ELSE, LITERAL_FOR, LITERAL_IF, LITERAL_WHILE"/>
        </module>
        <!--    检查左花括号附近是否存在代码, 即 大括号应该和代码在同一行，而不是单独一行-->
        <module name="LeftCurly">
            <property name="tokens"
                      value="ANNOTATION_DEF, CLASS_DEF, CTOR_DEF, ENUM_CONSTANT_DEF, ENUM_DEF,
                    INTERFACE_DEF, LAMBDA, LITERAL_CASE, LITERAL_CATCH, LITERAL_DEFAULT,
                    LITERAL_DO, LITERAL_ELSE, LITERAL_FINALLY, LITERAL_FOR, LITERAL_IF,
                    LITERAL_SWITCH, LITERAL_SYNCHRONIZED, LITERAL_TRY, LITERAL_WHILE, METHOD_DEF,
                    OBJBLOCK, STATIC_INIT, RECORD_DEF, COMPACT_CTOR_DEF"/>
        </module>
        <!--    检查右花括号附近是否存在代码, 即 大括号应该和代码在同一行，而不是单独一行-->
        <module name="RightCurly">
            <property name="id" value="RightCurlySame"/>
            <property name="tokens"
                      value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE,
                    LITERAL_DO"/>
        </module>
        <!--    检查右花括号附近是否存在代码, 即 大括号应该单独一行的情况-->
        <module name="RightCurly">
            <property name="id" value="RightCurlyAlone"/>
            <property name="option" value="alone"/>
            <property name="tokens"
                      value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, STATIC_INIT,
                    INSTANCE_INIT, ANNOTATION_DEF, ENUM_DEF, INTERFACE_DEF, RECORD_DEF,
                    COMPACT_CTOR_DEF"/>
        </module>
        <module name="SuppressionXpathSingleFilter">
            <!-- suppresion is required till https://github.com/checkstyle/checkstyle/issues/7541 -->
            <property name="id" value="RightCurlyAlone"/>
            <property name="query" value="//RCURLY[parent::SLIST[count(./*)=1]
                                     or preceding-sibling::*[last()][self::LCURLY]]"/>
        </module>
        <!--    检查特定的 token 后面应该存在空格-->
<!--
        <module name="WhitespaceAfter">
            <property name="tokens"
                      value="COMMA, SEMI, TYPECAST, LITERAL_IF, LITERAL_ELSE,
                    LITERAL_WHILE, LITERAL_DO, LITERAL_FOR, DO_WHILE"/>
        </module>
-->
        <!--    检查特定的token 是否被空格包围-->
<!--
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyLambdas" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <property name="ignoreEnhancedForColon" value="false"/>
            <property name="tokens"
                      value="ASSIGN, BAND, BAND_ASSIGN, BOR, BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR,
                    BXOR_ASSIGN, COLON, DIV, DIV_ASSIGN, DO_WHILE, EQUAL, GE, GT, LAMBDA, LAND,
                    LCURLY, LE, LITERAL_CATCH, LITERAL_DO, LITERAL_ELSE, LITERAL_FINALLY,
                    LITERAL_FOR, LITERAL_IF, LITERAL_RETURN, LITERAL_SWITCH, LITERAL_SYNCHRONIZED,
                    LITERAL_TRY, LITERAL_WHILE, LOR, LT, MINUS, MINUS_ASSIGN, MOD, MOD_ASSIGN,
                    NOT_EQUAL, PLUS, PLUS_ASSIGN, QUESTION, RCURLY, SL, SLIST, SL_ASSIGN, SR,
                    SR_ASSIGN, STAR, STAR_ASSIGN, LITERAL_ASSERT, TYPE_EXTENSION_AND"/>
            <message key="ws.notFollowed"
                     value="WhitespaceAround: ''{0}'' 后面允许跟随空格. 仅允许使用 '{}' 来表示空代码块."/>
            <message key="ws.notPreceded"
                     value="WhitespaceAround: ''{0}'' 前面没有空格."/>
        </module>
-->
        <!--检查一行是否只有一个语句-->
        <module name="OneStatementPerLine"/>
        <!--检查一行只声明一个变量-->
        <module name="MultipleVariableDeclarations"/>
        <!--    检查数组声明的形式为 String[] args ，而不是 String args[]-->
        <module name="ArrayTypeStyle"/>
        <!--    检查是否未声明 switch 的 default  语句-->
        <module name="MissingSwitchDefault"/>
        <!--    检查 switch 语句中是否有遗漏了 break 的 case 语句-->
        <module name="FallThrough">
            <property name="severity" value="error"/>
        </module>
        <!--      检查long类型的字面量声明应该使用 L 而不是 l（小写L）后者容易被误认为数字1-->
        <module name="UpperEll">
            <property name="severity" value="error"/>
        </module>
        <!--
              检查 Java 修饰符的声明顺序是否以 JLS 规范声明
            <module name="ModifierOrder"/>
        -->
        <!--
              检查指定的 token 语句前应该有一个空行
            <module name="EmptyLineSeparator">
              <property name="tokens"
                        value="PACKAGE_DEF, IMPORT, STATIC_IMPORT, CLASS_DEF, INTERFACE_DEF, ENUM_DEF,
                            STATIC_INIT, INSTANCE_INIT, METHOD_DEF, CTOR_DEF, VARIABLE_DEF, RECORD_DEF,
                            COMPACT_CTOR_DEF"/>
              <property name="allowNoEmptyLineBetweenFields" value="true"/>
            </module>
        -->
        <!--      检查点（ . ） 应该出现在换行的行首，如方法调用 -->
        <module name="SeparatorWrap">
            <property name="severity" value="error"/>
            <property name="id" value="SeparatorWrapDot"/>
            <property name="tokens" value="DOT"/>
            <property name="option" value="nl"/>
        </module>
        <!--      检查逗号（ , ） 应该出现在换行的行末，如参数定义 -->
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapComma"/>
            <property name="tokens" value="COMMA"/>
            <property name="option" value="EOL"/>
        </module>
        <!--      检查可变参数符（ ... ） 应该出现在换行的行末，如可变参数定义 -->
        <module name="SeparatorWrap">
            <!-- ELLIPSIS is EOL until https://github.com/google/styleguide/issues/259 -->
            <property name="id" value="SeparatorWrapEllipsis"/>
            <property name="tokens" value="ELLIPSIS"/>
            <property name="option" value="EOL"/>
        </module>
        <!--      检查数组声明符号（ [] ） 应该出现在换行的行末 -->
        <module name="SeparatorWrap">
            <!-- ARRAY_DECLARATOR is EOL until https://github.com/google/styleguide/issues/258 -->
            <property name="id" value="SeparatorWrapArrayDeclarator"/>
            <property name="tokens" value="ARRAY_DECLARATOR"/>
            <property name="option" value="EOL"/>
        </module>
        <!--      检查方法引用符号（ :: ） 应该出现在换行的行首 -->
        <module name="SeparatorWrap">
            <property name="severity" value="error"/>
            <property name="id" value="SeparatorWrapMethodRef"/>
            <property name="tokens" value="METHOD_REF"/>
            <property name="option" value="nl"/>
        </module>
        <!--      检查包名命名规则是否合法-->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern"
                     value="包名 ''{0}'' 不符合包名规范表达式： ''{1}''."/>
        </module>
        <!--
              检查类名是否合法
            <module name="TypeName">
              <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF,
                            ANNOTATION_DEF, RECORD_DEF"/>
              <message key="name.invalidPattern"
                       value="Type name ''{0}'' 不符合表达式： ''{1}''."/>
            </module>
        -->
        <!--      检查类成员变量名是否合法-->
<!--
        <module name="MemberName">
            <property name="severity" value="error"/>
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern"
                     value="成员变量名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查方法参数、构造参数名是否合法-->
<!--
        <module name="ParameterName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="参数名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查 lambda 表达式参数名是否合法-->
<!--
        <module name="LambdaParameterName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="Lambda 表达式参数名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查 catch 语句的参数名是否合法-->
<!--
        <module name="CatchParameterName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="Catch 语句参数名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查本地变量名是否合法-->
<!--
        <module name="LocalVariableName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="本地变量名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查模式匹配的变量名是否合法，模式匹配是 Java17 的功能-->
<!--
        <module name="PatternVariableName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="模式匹配变量名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--
              检查类的泛型参数名是否合法
            <module name="ClassTypeParameterName">
              <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
              <message key="name.invalidPattern"
                       value="Class type name ''{0}'' 不符合表达式： ''{1}''."/>
            </module>
        -->
        <!--      检查 Record 的参数名是否合法，Record 是 Java17 的功能-->
<!--
        <module name="RecordComponentName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="Record 组件名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查 Record 的泛型参数名是否合法-->
<!--
        <module name="RecordTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern"
                     value="Record 泛型参数名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查方法的泛型参数名是否合法-->
<!--
        <module name="MethodTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern"
                     value="方法的泛型参数名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查接口的泛型参数名是否合法-->
<!--
        <module name="InterfaceTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern"
                     value="接口的泛型参数名 ''{0}'' 不符合表达式： ''{1}''."/>
        </module>
-->
        <!--      检查是否使用了 finalizer，finalizer 是 Java 早期为了兼容 C 程序员的习惯而设计的接口，
        但是它并不安全，任何关于Java规范中都强烈建议禁止使用-->
        <module name="NoFinalizer">
            <property name="severity" value="error"/>
        </module>
        <!--      检查泛型声明周围的空格是否正确-->
<!--
        <module name="GenericWhitespace">
            <message key="ws.followed"
                     value="GenericWhitespace ''{0}'' 后跟随空格."/>
            <message key="ws.preceded"
                     value="GenericWhitespace ''{0}'' 前面有空格."/>
            <message key="ws.illegalFollow"
                     value="GenericWhitespace ''{0}'' 后面应该添加空格."/>
            <message key="ws.notPreceded"
                     value="GenericWhitespace ''{0}'' 前面应该添加空格."/>
        </module>
-->
        <!--      检查代码缩进-->
        <module name="Indentation">
            <property name="severity" value="error"/>
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="2"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="4"/>
            <property name="arrayInitIndent" value="2"/>
        </module>
        <!--      检查指定 token 不能有连续的大写字符, 如类定义， 方法定义-->
<!--        <module name="AbbreviationAsWordInName">-->
<!--            <property name="ignoreFinal" value="false"/>-->
<!--            <property name="allowedAbbreviationLength" value="0"/>-->
<!--            <property name="tokens"-->
<!--                      value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, ANNOTATION_DEF, ANNOTATION_FIELD_DEF,-->
<!--                    PARAMETER_DEF, VARIABLE_DEF, METHOD_DEF, PATTERN_VARIABLE_DEF, RECORD_DEF,-->
<!--                    RECORD_COMPONENT_DEF"/>-->
<!--        </module>-->
        <!--      检查 switch 表达式中冒号前面没有空格-->
<!--        <module name="NoWhitespaceBeforeCaseDefaultColon"/>-->
        <!--      检查重载的方法应该成组出现-->
<!--        <module name="OverloadMethodsDeclarationOrder"/>-->
        <!--      检查变量的定义与第一次使用的距离是否过远（不应该超过5行）-->
        <module name="VariableDeclarationUsageDistance">
            <property name="severity" value="error"/>
            <property name="allowedDistance" value="5"/>
        </module>
        <!--
              检查导入语句的顺序是否是按照指定顺序分组声明的
            <module name="CustomImportOrder">
              &lt;!&ndash;      <property name="sortImportsInGroupAlphabetically" value="true"/>&ndash;&gt;
              <property name="separateLineBetweenGroups" value="true"/>
              <property name="customImportOrderRules" value="THIRD_PARTY_PACKAGE###STANDARD_JAVA_PACKAGE###STATIC"/>
              <property name="tokens" value="IMPORT, STATIC_IMPORT, PACKAGE_DEF"/>
            </module>
        -->
        <!--
              检查方法定义、方法调用、构造函数定义、构造函数调用是否具有合法的空格填充，比如方法声明时， 右括号与大括号之间应该留有一个空格
            <module name="MethodParamPad">
              <property name="tokens"
                        value="CTOR_DEF, LITERAL_NEW, METHOD_CALL, METHOD_DEF,
                            SUPER_CTOR_CALL, ENUM_CONSTANT_DEF, RECORD_DEF"/>
            </module>
        -->
        <!--      检查指定 token 前不应该存在空格-->
<!--
        <module name="NoWhitespaceBefore">
            <property name="tokens"
                      value="COMMA, SEMI, POST_INC, POST_DEC, DOT,
                    LABELED_STAT, METHOD_REF"/>
            <property name="allowLineBreaks" value="true"/>
        </module>
-->
        <!--      检查括号内的空格填充是否合法, 比如括号内的第一个参数与左括号之间不应该留有空格-->
<!--
        <module name="ParenPad">
            <property name="tokens"
                      value="ANNOTATION, ANNOTATION_FIELD_DEF, CTOR_CALL, CTOR_DEF, DOT, ENUM_CONSTANT_DEF,
                    EXPR, LITERAL_CATCH, LITERAL_DO, LITERAL_FOR, LITERAL_IF, LITERAL_NEW,
                    LITERAL_SWITCH, LITERAL_SYNCHRONIZED, LITERAL_WHILE, METHOD_CALL,
                    METHOD_DEF, QUESTION, RESOURCE_SPECIFICATION, SUPER_CTOR_CALL, LAMBDA,
                    RECORD_DEF"/>
        </module>
-->
        <!--      检查操作符左右的换行是否合法-->
        <module name="OperatorWrap">
            <property name="option" value="NL"/>
            <property name="tokens"
                      value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR,
                    LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR, METHOD_REF,
                    TYPE_EXTENSION_AND "/>
        </module>
        <!--      检查注解的位置是否合法-->
        <module name="AnnotationLocation">
            <property name="severity" value="error"/>
            <property name="id" value="AnnotationLocationMostCases"/>
            <property name="tokens"
                      value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF,
                      RECORD_DEF, COMPACT_CTOR_DEF"/>
        </module>
        <module name="AnnotationLocation">
            <property name="severity" value="error"/>
            <property name="id" value="AnnotationLocationVariables"/>
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="allowSamelineMultipleAnnotations" value="true"/>
        </module>
        <!--      检查 Javadoc 标签后面是否具有描述信息-->
        <module name="NonEmptyAtclauseDescription">
            <property name="severity" value="error"/>
        </module>
        <!--      检查 Javadoc 的位置是否合法-->
        <module name="InvalidJavadocPosition"/>
        <!--    检查 Javadoc tag 块的缩进是否正确-->
        <module name="JavadocTagContinuationIndentation"/>
        <!--
            检查 Javadoc 中的简述语句是否符合规则
            <module name="SummaryJavadoc">
              <property name="forbiddenSummaryFragments"
                        value="^@return the *|^This method returns |^A [{]@code [a-zA-Z0-9]+[}]( is a )"/>
            </module>
        -->
        <!--
            检查 Javadoc 中的段落规则
            <module name="JavadocParagraph"/>
        -->
        <!--
            检查 Javadoc 的第一个块标签之前必须存在一个空行
            <module name="RequireEmptyLineBeforeBlockTagGroup"/>
        -->
        <!--
            检查 Javadoc 的块标签声明的顺序是否正确
            <module name="AtclauseOrder">
              <property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
              <property name="target"
                        value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
            </module>
        -->
        <!--    检查方法的 Javadoc 是否符合规则-->
        <module name="JavadocMethod">
            <property name="accessModifiers" value="public"/>
            <property name="allowMissingParamTags" value="true"/>
            <property name="allowMissingReturnTag" value="true"/>
            <property name="allowedAnnotations" value="Override, Test"/>
            <property name="tokens" value="METHOD_DEF, CTOR_DEF, ANNOTATION_FIELD_DEF, COMPACT_CTOR_DEF"/>
        </module>
        <!--    检查方法是否缺少 Javadoc-->
<!--        <module name="MissingJavadocMethod">-->
<!--            <property name="scope" value="public"/>-->
<!--            <property name="minLineCount" value="2"/>-->
<!--            <property name="allowedAnnotations" value="Override, Test"/>-->
<!--            <property name="tokens" value="METHOD_DEF, CTOR_DEF, ANNOTATION_FIELD_DEF,-->
<!--                                   COMPACT_CTOR_DEF"/>-->
<!--        </module>-->
        <!--    检查类是否缺少 Javadoc-->
<!--        <module name="MissingJavadocType">-->
<!--            <property name="scope" value="protected"/>-->
<!--            <property name="tokens"-->
<!--                      value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF,-->
<!--                      RECORD_DEF, ANNOTATION_DEF"/>-->
<!--            <property name="excludeScope" value="nothing"/>-->
<!--        </module>-->
        <!--    检查方法名称是否符合规则-->
        <module name="MethodName">
            <property name="severity" value="error"/>
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9_]*$"/>
            <message key="name.invalidPattern"
                     value="方法名 ''{0}'' 必须符合此模式: ''{1}''."/>
        </module>
        <!--
            检查单行的 Javadoc 是否符合规则
            <module name="SingleLineJavadoc"/>
        -->
        <!--    检查是否存在空的 catch 块-->
        <module name="EmptyCatchBlock">
            <property name="severity" value="error"/>
            <property name="exceptionVariableName" value="ignored"/>
        </module>
        <!--    检查注释与被注释代码的缩进-->
        <module name="CommentsIndentation">
            <property name="tokens" value="SINGLE_LINE_COMMENT, BLOCK_COMMENT_BEGIN"/>
        </module>
        <!-- https://checkstyle.org/config_filters.html#SuppressionXpathFilter -->
        <module name="SuppressionXpathFilter">
            <property name="file" value="${org.checkstyle.google.suppressionxpathfilter.config}"
                      default="checkstyle-xpath-suppressions.xml" />
            <property name="optional" value="true"/>
        </module>
        <!--    抑制注解容器，收集源代码中的 @SuppressWarning 注解，提供给其他模块使用-->
        <module name="SuppressWarningsHolder" />
        <!--    抑制注释过滤器，使用注释来控制是否启用 checkstyle 检查-->
        <module name="SuppressionCommentFilter">
            <property name="offCommentFormat" value="CHECKSTYLE.OFF\: ([\w\|]+)" />
            <property name="onCommentFormat" value="CHECKSTYLE.ON\: ([\w\|]+)" />
            <property name="checkFormat" value="$1" />
        </module>
        <!--    抑制注释过滤器，使用注释来控制是否启用 checkstyle 检查，与SuppressionCommentFilter不同的点是，此过滤器是行内内联的-->
        <module name="SuppressWithNearbyCommentFilter">
            <property name="commentFormat" value="CHECKSTYLE.SUPPRESS\: ([\w\|]+)"/>
            <!-- $1 refers to the first match group in the regex defined in commentFormat -->
            <property name="checkFormat" value="$1"/>
            <!-- The check is suppressed in the next line of code after the comment -->
            <property name="influenceFormat" value="1"/>
        </module>
    </module>
</module>
