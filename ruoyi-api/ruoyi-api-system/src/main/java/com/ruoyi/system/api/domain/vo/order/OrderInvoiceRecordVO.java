package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:14
 */
@Data
public class OrderInvoiceRecordVO implements Serializable {
    private static final long serialVersionUID = 1604628432963271341L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 发票ID (FK:order_invoice.id)
     */
    @ApiModelProperty(value = "发票ID")
    private Long invoiceId;

    /**
     * 类型（1：发票，2：红冲）
     */
    @ApiModelProperty(value = "类型（1：发票，2：红冲）")
    private Integer type;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 发票号 or 红冲票号
     */
    @ApiModelProperty(value = "发票号 or 红冲票号")
    private String number;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date invoicingTime;

    /**
     * 发票URI
     */
    @ApiModelProperty(value = "发票URI")
    private String objectKey;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发票文件URI")
    private List<String> objectKeys;

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
        if (StrUtil.isNotBlank(objectKey)) {
            this.objectKeys = StrUtil.split(objectKey, StrUtil.COMMA);
        }
    }
}
