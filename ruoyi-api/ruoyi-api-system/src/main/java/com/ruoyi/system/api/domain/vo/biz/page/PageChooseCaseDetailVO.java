package com.ruoyi.system.api.domain.vo.biz.page;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;


/**
 * @program :woniu-world
 * @description :
 * <AUTHOR>
 * @create :2024-08-22 09:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageChooseCaseDetailVO implements Serializable {
    private static final long serialVersionUID = -1284578778051683330L;

    @ApiModelProperty("标题")
    @NotBlank(message = "[标题]不能为空")
    @Size(max = 32, message = "[标题]支持最多输入32字符")
    private String name;

    @ApiModelProperty("图片地址")
    @NotBlank(message = "[图片地址]不能为空")
    private String imageUrl;

    @ApiModelProperty("跳转类型（1-视频分组、2-视频链接、3-不跳转）")
    @NotNull(message = "[跳转类型]不能为空")
    private Integer skipType;

    @ApiModelProperty("跳转uri")
    @NotNull(message = "[跳转uri]不能为空")
    private String skipUri;

    @ApiModelProperty("分组名称")
    private String groupName;
}
