package com.ruoyi.system.api.domain.vo.biz.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-19 18:41
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class VideoShopWindowVO implements Serializable {
    private static final long serialVersionUID = -7406770749490349181L;

    @ApiModelProperty("橱窗名称")
    @Size(max = 32, message = "橱窗[标题]支持最多输入32字符")
    private String name;

    @ApiModelProperty("布局类型")
    @NotNull(message = "橱窗[布局类型]不能为空")
    private Integer layoutType;

    @ApiModelProperty("跳转类型: 跳转类型（1-视频分组、2-视频链接、3-不跳转）")
    private Integer skipType;

    @ApiModelProperty("跳转uri")
    @NotNull(message = "橱窗[跳转uri]不能为空")
    private String skipUri;

    @ApiModelProperty("分组名称")
    private String groupName;
}
