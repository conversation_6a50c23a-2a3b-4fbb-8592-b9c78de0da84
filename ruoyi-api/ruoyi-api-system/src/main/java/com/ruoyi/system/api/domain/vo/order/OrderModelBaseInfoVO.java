package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单详情（基础信息）
 *
 * <AUTHOR>
 * @date 2024/7/2 9:38
 */
@Data
public class OrderModelBaseInfoVO implements Serializable {
    private static final long serialVersionUID = -834637676436125786L;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long id;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    @JsonIgnore
    private Long rollbackId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    @JsonIgnore
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    @JsonIgnore
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 参考图片（关联资源id）
     */
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<OrderVideoContent> shootRequired;

    /**
     * 产品卖点
     */
    @ApiModelProperty("产品卖点")
    private String sellingPointProduct;

    /** 实物（0:是,1:不是） */
    @ApiModelProperty(value = "实物（0:是,1:不是）")
    private Integer isObject;

    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty(value = "拍摄模特注意事项")
    private String shootAttention;

    /**
     * 需要提醒拍摄模特注意事项
     */
    @ApiModelProperty(value = "需要提醒拍摄模特注意事项")
    private Integer needRemindShootAttention;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    private String shootAttentionObjectKey;
}
