package com.ruoyi.system.api.domain.vo.biz.page;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


/**
 * @program :woniu-world
 * @description :
 * <AUTHOR>
 * @create :2024-08-22 09:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageChooseCaseVO implements Serializable {
    private static final long serialVersionUID = -1284578778051683330L;

    @ApiModelProperty("模块类型：1-海报,2-金刚区,3-瓷片区")
    @NotNull(message = "[模块类型]不能为空")
    private Integer moduleType;

    @ApiModelProperty("布局类型")
    private Integer layoutType;

    @ApiModelProperty("内容列表")
    @Valid
    @Size(min = 1, message = "至少需要有一条对应数据")
    private List<PageChooseCaseDetailVO> pageChooseCaseDetailList;
}
