package com.ruoyi.system.api.domain.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/12 10:37
 */
@Data
public class OrderMemberPayInfoVO implements Serializable {
    private static final long serialVersionUID = 2131938246466896068L;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "订单金额（单位：￥）")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "税点（单位：%）")
    private BigDecimal taxPoint;

    @ApiModelProperty(value = "税点费用（单位：￥）")
    private BigDecimal taxPointCost;

    @ApiModelProperty(value = "还需支付金额（单位：￥）")
    private BigDecimal payAmount;
}
