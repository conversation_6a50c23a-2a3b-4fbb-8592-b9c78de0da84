package com.ruoyi.system.api.domain.vo.biz.model;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/11 17:02
 */
@Data
public class ModelChangeRecordVO implements Serializable {

    private static final long serialVersionUID = -3231983820255718536L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模特id
     */
    @ApiModelProperty("模特id")
    private Long modelId;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date operateTime;

    /**
     * 操作人姓名
     */
    @ApiModelProperty("操作人姓名")
    private String operateUserName;

    /**
     * 操作类型（1:新增模特,2:修改模特信息,3:变更状态）
     */
    @ApiModelProperty("操作类型（1:新增模特,2:修改模特信息,3:变更状态）")
    private Integer operateType;

    /**
     * 操作详情
     */
    @ApiModelProperty("操作详情")
    private String operateDetail;

    /**
     * 操作说明
     */
    @ApiModelProperty("操作说明")
    private String operateExplain;
}
