package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 19:29
 */
@Data
public class OrderInvoiceDetailVO implements Serializable {
    private static final long serialVersionUID = 8199405809751413227L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单类型（0:视频订单,1:会员订单）
     */
    @ApiModelProperty(value = "订单类型（0:视频订单,1:会员订单）")
    private Integer orderType;

    /**
     * 开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）
     */
    @ApiModelProperty(value = "开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）")
    private Integer status;

    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id")
    @JsonIgnore
    private Long merchantId;

    /**
     * 商家信息
     */
    @ApiModelProperty(value = "商家信息")
    private BusinessVO merchant;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    private String merchantCode;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal payAmount;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 发票类型（1：增值税普通发票，2：形式发票）
     */
    @ApiModelProperty(value = "发票类型（1：增值税普通发票，2：形式发票）")
    private Integer invoiceType;

    /**
     * 抬头类型（1：企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位）")
    private Integer titleType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    private String title;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String dutyParagraph;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司联系人
     */
    @ApiModelProperty(value = "公司联系人")
    private String companyContact;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI")
    private String attachmentObjectKey;

    /**
     * 发票内容
     */
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 发票备注
     */
    @ApiModelProperty(value = "发票备注")
    private String invoiceRemark;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date invoicingTime;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    private String number;

    /**
     * 发票文件URI
     */
    @ApiModelProperty(value = "发票文件URI")
    private String objectKey;

    @ApiModelProperty(value = "发票文件URI")
    private List<String> objectKeys;

    /**
     * 注意事项
     */
    @ApiModelProperty(value = "注意事项")
    private String cautions;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    @JsonIgnore
    private Long submitById;

    /**
     * 提交人
     */
    @ApiModelProperty(value = "提交人")
    private BusinessAccountVO submitter;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 运营端-关联订单
     */
    @ApiModelProperty(value = "运营端-关联订单")
    private List<OrderInvoiceVideoBackVO> orderInvoiceVideoBackVOS;

    /**
     * 商家端-关联订单
     */
    @ApiModelProperty(value = "商家端-关联订单")
    private List<OrderInvoiceVideoCompanyVO> orderInvoiceVideoCompanyVOS;

    /**
     * 运营端-红冲关联订单
     */
    @ApiModelProperty(value = "运营端-红冲关联订单")
    private List<OrderInvoiceRedOrderVideoVO> orderInvoiceRedOrderVideoVOS;

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
        if (StrUtil.isNotBlank(objectKey)) {
            this.objectKeys = StrUtil.split(objectKey, StrUtil.COMMA);
        }
    }
}
