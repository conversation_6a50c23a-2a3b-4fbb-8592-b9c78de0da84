package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :订单会员表
 * @create :2024-06-24 16:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberDetailVO implements Serializable {

    private static final long serialVersionUID = -7105337902246005875L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "订单主键ID")
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付金额（单位：$）")
    private BigDecimal payAmountDollar;

    @ApiModelProperty(value = "当前汇率")
    private BigDecimal currentExchangeRate;

    @ApiModelProperty(value = "是否默认汇率")
    private Boolean isDefaultExchangeRate;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty(value = "订单实付金额（对应币种实付）")
    private BigDecimal realPayAmountCurrency;


    @ApiModelProperty(value = "币种（详见sys_dict_type.dict_type = sys_money_type）")
    private Integer currency;

    @ApiModelProperty(value = "使用余额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "剩余支付金额")
    private BigDecimal surplusAmount;

    @ApiModelProperty(value = "下单用户id")
    private Long orderUserId;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    @ApiModelProperty(value = "支付用户id")
    private Long payUserId;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty(value = "支付账户（对公）")
    @Excel(name = "支付账户（对公）")
    private String payAccount;

    @ApiModelProperty(value = "财务审核状态（0:待审核,1:审核通过,2.审核异常,3.已关闭")
    @Excel(name = "财务审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "支付方式（1:微信,2:支付宝,3:云闪付/银联,4.数字人民币,5.银行,6:对公,7:全币种,10:余额,11:微信+余额,12:支付宝+余额,13:云闪付/银联+余额,14.数字人民币+余额,15.银行+余额,16:对公+余额,17:全币种+余额）")
    @Excel(name = "支付方式")
    private Integer payType;

    @ApiModelProperty(value = "订单备注")
    @Excel(name = "订单备注")
    private String orderRemark;

    @NotNull(message="[订单状态(1待支付、2待审核、3交易成功、4交易关闭)]不能为空")
    @ApiModelProperty("订单状态(1待支付、2待审核、3交易成功、4交易关闭)")
    private Integer status;

    @NotNull(message="[套餐类型：0-季度套餐，1-一年会员，2-三年会员]不能为空")
    @ApiModelProperty("套餐类型：0-季度套餐，1-一年会员，2-三年会员")
    private Integer packageType;

    @ApiModelProperty("开票信息")
    private OrderInvoiceVO orderInvoiceVO;

    @ApiModelProperty("商家账号数据")
    private BusinessAccountDetailVO businessAccountDetailVO;

    @ApiModelProperty("加赠时间")
    private Integer presentedTime;

    @ApiModelProperty("加赠时间类型（1-天,2-月,3-年）")
    private Integer presentedTimeType;
    /**
     * 种草码优惠金额（单位：￥）
     */
    @ApiModelProperty(value = "种草码优惠金额（单位：￥）")
    private BigDecimal seedCodeDiscount;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @ApiModelProperty(value = "种草码")
    private String seedCode;

    @ApiModelProperty(value = "种草官ID")
    private String seedId;

    @ApiModelProperty(value = "会员结算类型（1-固定金额，2-固定比例）")
    private Integer memberDiscountType;

    @ApiModelProperty(value = "结算比例")
    private BigDecimal settleRage;

    @ApiModelProperty("套餐价格")
    private BigDecimal packageAmount;

    @ApiModelProperty(value = "订单金额（单位：￥）")
    private BigDecimal orderAmount;

    /**
     * 下单用户账号
     */
    @ApiModelProperty(value = "下单用户账号")
    private String orderUserAccount;

    /**
     * 下单用户名称
     */
    @ApiModelProperty(value = "下单用户名称")
    private String orderUserName;

    /**
     * 下单用户微信名称
     */
    @ApiModelProperty(value = "下单用户微信名称")
    private String orderUserNickName;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;
}

