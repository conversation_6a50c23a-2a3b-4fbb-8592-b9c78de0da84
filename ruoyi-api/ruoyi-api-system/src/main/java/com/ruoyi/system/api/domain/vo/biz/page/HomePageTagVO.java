package com.ruoyi.system.api.domain.vo.biz.page;/**
 * @Title: HomePageTagVO
 * <AUTHOR>
 * @Package com.ruoyi.system.api.domain.vo.biz.page
 * @Date 2024/8/20 18:35
 * @description:
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program :woniu-world
 * @description :
 * <AUTHOR>
 * @create :2024-08-20 18:35
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HomePageTagVO implements Serializable {
    private static final long serialVersionUID = 6564828092073465755L;

    @ApiModelProperty("分类名称")
    @NotBlank(message = "[分类名称]不能为空")
    private String categoryName;

    @ApiModelProperty("是否所有类目")
    @NotNull(message = "[是否所有类目]不能为空")
    private Integer isAll;

    @ApiModelProperty("标签Id")
    private Long tagId;

    @ApiModelProperty("标签名称")
    private String tagName;



}
