databaseChangeLog:
  - logicalFilePath: 'changelog-dwy-1.0.yml'
  - changeSet:
      id: dwy-order-1
      author: dwy
      changes:
        # 添加列
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
        # 设置列备注
        - setColumnRemarks:
            tableName: order_video
            columnName: create_order_user_id
            columnDataType: bigint(20)
            remarks: 创建订单用户id
  - changeSet:
      id: dwy-order-2
      author: dwy
      changes:
        - addNotNullConstraint:
            columnDataType: bigint(20)
            columnName: create_order_user_id
            tableName: order_video
            constraintName: NOT NULL
            defaultNullValue: 0
  - changeSet:
      id: dwy-order-3
      author: dwy
      changes:
        # 设置列备注
        - setColumnRemarks:
            tableName: order_video
            columnName: create_order_user_id
            columnDataType: bigint(20)
            remarks: 创建订单用户id
  - changeSet:
      id: dwy-order-4
      author: dwy
      changes:
        - addNotNullConstraint:
            columnDataType: bigint(20)
            columnName: create_order_user_id
            tableName: order_video
            constraintName: NOT NULL
            defaultNullValue: 0
        # 设置列备注
        - setColumnRemarks:
            tableName: order_video
            columnName: create_order_user_id
            columnDataType: bigint(20)
            remarks: 创建订单用户id
  - changeSet:
      id: dwy-order-5
      author: dwy
      changes:
        - addNotNullConstraint:
            columnDataType: bigint(20)
            columnName: create_order_user_id
            tableName: order_video
            constraintName: NOT NULL
            defaultNullValue: 0
            remarks: 创建订单用户id
  - changeSet:
      id: dwy-order-6
      author: dwy
      changes:
        # 设置列备注
        - setColumnRemarks:
            tableName: order_video
            columnName: create_order_user_id
            columnDataType: bigint(20)
            remarks: 创建订单用户id
  - changeSet:
      id: dwy-order-7
      author: dwy
      changes:
        - createIndex:
            tableName: order_video
            indexName: uk_video_code
            unique: true
            columns:
              - column:
                  name: video_code
        - createIndex:
            tableName: order_video
            indexName: idx_order_num
            unique: false
            columns:
              - column:
                  name: order_num
        - createIndex:
            tableName: order_note
            indexName: uk_order_num
            unique: true
            columns:
              - column:
                  name: order_num
        - createIndex:
            tableName: order_task
            indexName: uk_task_num
            unique: true
            columns:
              - column:
                  name: task_num
        - createIndex:
            tableName: order_video_model
            indexName: uk_video_id
            unique: true
            columns:
              - column:
                  name: video_id
        - createIndex:
            tableName: order_video_refund
            indexName: uk_refund_num
            unique: true
            columns:
              - column:
                  name: refund_num
  - changeSet:
      id: dwy-order-8
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: order_video
            columnName: platform
            columnDataType: tinyint(1)
            remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
  - changeSet:
      id: dwy-order-9
      author: dwy
      changes:
        - addNotNullConstraint:
            columnDataType: tinyint(1)
            columnName: platform
            tableName: order_video
            remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
  - changeSet:
      id: dwy-order-10
      author: dwy
      changes:
        - addNotNullConstraint:
            columnDataType: tinyint(1)
            columnName: platform
            tableName: order_video
        - setColumnRemarks:
            tableName: order_video
            columnName: platform
            columnDataType: tinyint(1)
            remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
  - changeSet:
      id: dwy-order-11
      author: dwy
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_video` 
              MODIFY COLUMN `platform` tinyint(1) NOT NULL COMMENT '使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `status`;
  - changeSet:
      id: dwy-order-12
      author: dwy
      changes:
        - sql:
            sql: |
              ALTER TABLE `order_video` 
              MODIFY COLUMN `product_pic` bigint(20) NULL DEFAULT NULL COMMENT '产品图（FK:resource.id）' AFTER `product_link`,
              MODIFY COLUMN `model_type` tinyint(1) NOT NULL COMMENT '模特类型（0:影响者,1:素人）' AFTER `shooting_country`,
              MODIFY COLUMN `is_object` tinyint(1) NOT NULL COMMENT '是否需要发货（0:需要,1:不需要）' AFTER `model_type`,
              MODIFY COLUMN `reference_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参考图片（FK:resource.id，多个用,隔开）' AFTER `pic_count`,
              MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注图片（FK:resource.id，多个用,隔开）' AFTER `shipping_remark`,
              MODIFY COLUMN `create_order_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建订单用户id' AFTER `un_match_flag`;
  - changeSet:
      id: dwy-order-13
      author: dwy
      changes:
        - modifyDataType:
            tableName: order_invoice
            columnName: amount
            newDataType: decimal(12,2)
  - changeSet:
      id: dwy-order-14
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: order_invoice
            columnName: amount
            columnDataType: decimal(12,1)
            remarks: 开票金额
  - changeSet:
      id: dwy-order-15
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: order_invoice
            columnName: amount
            columnDataType: decimal(12,2)
            remarks: 开票金额
  - changeSet:
      id: dwy-order-16
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `order_amount` decimal(12, 2) NOT NULL COMMENT '订单金额（单位：￥）' AFTER `video_count`,
            MODIFY COLUMN `pay_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '支付金额（单位：￥）' AFTER `order_amount`,
            MODIFY COLUMN `use_balance` decimal(12, 2) NOT NULL DEFAULT 0.0 COMMENT '使用余额（单位：￥）' AFTER `current_exchange_rate`,
            MODIFY COLUMN `tax_point` decimal(12, 2) NULL DEFAULT NULL COMMENT '税点（单位：%）' AFTER `use_balance`,
            MODIFY COLUMN `tax_point_cost` decimal(12, 2) NULL DEFAULT NULL COMMENT '税点费用（单位：￥）' AFTER `tax_point`;
        - setColumnRemarks:
            tableName: order_video
            columnName: amount
            columnDataType: decimal(12,2)
            remarks: 视频金额（单位：￥）
        - setColumnRemarks:
            tableName: order_video
            columnName: video_price
            columnDataType: decimal(12,2)
            remarks: 视频价格（单位：$）
        - setColumnRemarks:
            tableName: order_video
            columnName: pic_price
            columnDataType: decimal(12,2)
            remarks: 图片费用（单位：$）
        - setColumnRemarks:
            tableName: order_video
            columnName: exchange_price
            columnDataType: decimal(12,2)
            remarks: 手续费（单位：$）
        - setColumnRemarks:
            tableName: order_video
            columnName: service_price
            columnDataType: decimal(12,2)
            remarks: 服务费（单位：$）
        - setColumnRemarks:
            tableName: order_video
            columnName: commission
            columnDataType: decimal(12,2)
            remarks: 模特佣金
  - changeSet:
      id: dwy-order-17
      author: dwy
      changes:
        - setColumnRemarks:
            tableName: order_video_refund
            columnName: amount
            columnDataType: decimal(12,2)
            remarks: 视频金额（单位：￥）
        - setColumnRemarks:
            tableName: order_video_refund
            columnName: refund_amount
            columnDataType: decimal(12,2)
            remarks: 退款金额（单位：￥）
  - changeSet:
      id: dwy-order-18
      author: dwy
      changes:
        - createTable:
            tableName: video_cart
            isNotExists: true
            remarks: 视频_购物车表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 60001
              - column:
                  name: platform
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
              - column:
                  name: video_format
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 视频格式（1:横屏16：9,2:竖屏9：16）
              - column:
                  name: shooting_country
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
              - column:
                  name: model_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 模特类型（0:影响者,1:素人）
              - column:
                  name: product_chinese
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 产品中文名
              - column:
                  name: product_english
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: 产品英文名
              - column:
                  name: product_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 产品链接
              - column:
                  name: intention_model_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 意向模特id
              - column:
                  name: reference_video_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 参考视频链接
              - column:
                  name: pic_count
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 照片数量（1:2张/$10,2:5张/$20）
              - column:
                  name: reference_pic
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 参考图片（FK:resource.id，多个用,隔开）
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 视频金额（单位：￥）
              - column:
                  name: create_order_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户id
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        - createTable:
            tableName: video_cart_content
            isNotExists: true
            remarks: 视频_购物车_关联内容表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 60001
              - column:
                  name: video_cart_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 购物车id FK:video_cart.id
              - column:
                  name: content
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 内容
              - column:
                  name: sort
                  type: int(2)
                  constraints:
                    nullable: false
                  remarks: 排序
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-19
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `reference_video_link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '参考视频链接' AFTER `product_pic`,
            MODIFY COLUMN `amount` decimal(12, 2) NOT NULL COMMENT '视频金额（单位：￥）' AFTER `issue_id`,
            MODIFY COLUMN `video_price` decimal(12, 2) NOT NULL COMMENT '视频价格（单位：$）' AFTER `amount`,
            MODIFY COLUMN `pic_price` decimal(12, 2) NOT NULL COMMENT '图片费用（单位：$）' AFTER `video_price`,
            MODIFY COLUMN `exchange_price` decimal(12, 2) NOT NULL COMMENT '手续费（单位：$）' AFTER `pic_price`,
            MODIFY COLUMN `service_price` decimal(12, 2) NOT NULL COMMENT '服务费（单位：$）' AFTER `exchange_price`,
            MODIFY COLUMN `create_order_user_id` bigint(20) NOT NULL COMMENT '创建订单用户id' AFTER `un_match_flag`;
  - changeSet:
      id: dwy-order-20
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
            ALTER TABLE `video_cart_content` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-21
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: create_order_business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  afterColumn: create_order_user_id
                  remarks: 创建订单商家id
  - changeSet:
      id: dwy-order-22
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `create_order_user_id` bigint(20) NOT NULL COMMENT '创建订单用户id' AFTER `amount`;
  - changeSet:
      id: dwy-order-23
      author: dwy
      changes:
        - dropTable:
            tableName: video_cart
        - dropTable:
            tableName: video_cart_content
  - changeSet:
      id: dwy-order-24
      author: dwy
      changes:
        - createTable:
            tableName: video_cart
            isNotExists: true
            remarks: 视频_购物车表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: platform
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
              - column:
                  name: video_format
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 视频格式（1:横屏16：9,2:竖屏9：16）
              - column:
                  name: shooting_country
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
              - column:
                  name: model_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 模特类型（0:影响者,1:素人）
              - column:
                  name: product_chinese
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 产品中文名
              - column:
                  name: product_english
                  type: varchar(50)
                  constraints:
                    nullable: false
                  remarks: 产品英文名
              - column:
                  name: product_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 产品链接
              - column:
                  name: intention_model_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 意向模特id
              - column:
                  name: reference_video_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 参考视频链接
              - column:
                  name: pic_count
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 照片数量（1:2张/$10,2:5张/$20）
              - column:
                  name: reference_pic
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 参考图片（FK:resource.id，多个用,隔开）
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 视频金额（单位：￥）
              - column:
                  name: create_order_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单用户id
              - column:
                  name: create_order_business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单商家id
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        - createTable:
            tableName: video_cart_content
            isNotExists: true
            remarks: 视频_购物车_关联内容表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: video_cart_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 购物车id FK:video_cart.id
              - column:
                  name: content
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 内容
              - column:
                  name: sort
                  type: int(2)
                  constraints:
                    nullable: false
                  remarks: 排序
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-25
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
            ALTER TABLE `video_cart_content` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-26
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `amount` decimal(12, 2) NOT NULL COMMENT '视频金额（单位：$）' AFTER `reference_pic`;
  - changeSet:
      id: dwy-order-27
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: product_pic
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 产品图（FK:resource.id）
                  afterColumn : product_link
  - changeSet:
      id: dwy-order-28
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `status` tinyint(2) NOT NULL COMMENT '订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）' AFTER `video_code`;
            ALTER TABLE `order_video_refund` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT NULL COMMENT '申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成）' AFTER `refund_num`;
  - changeSet:
      id: dwy-order-29
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video
            columns:
              - column:
                  name: recipient
              - column:
                  name: city
              - column:
                  name: state
              - column:
                  name: zipcode
              - column:
                  name: detail_address
              - column:
                  name: un_match_flag
  - changeSet:
      id: dwy-order-30
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: remark
                  type: varchar(150)
                  constraints:
                    nullable: true
                  afterColumn: number
                  remarks: 备注
  - changeSet:
      id: dwy-order-31
      author: dwy
      changes:
        - modifyDataType:
            tableName: order_video_logistic
            columnName: number
            newDataType: varchar(30)
  - changeSet:
      id: dwy-order-32
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            MODIFY COLUMN `number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号' AFTER `video_id`;
  - changeSet:
      id: dwy-order-33
      author: dwy
      changes:
        - createTable:
            tableName: order_video_roast
            isNotExists: true
            remarks: 订单_视频_吐槽表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频id FK:order_video.id
              - column:
                  name: object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 吐槽对象(1:视频,2:客服,3:其他)
              - column:
                  name: content
                  type: varchar(300)
                  constraints:
                    nullable: false
                  remarks: 吐槽内容
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-34
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video
            columns:
              - column:
                  name: allow_upload
              - column:
                  name: create_by
              - column:
                  name: update_by
              - column:
                  name: upload_platform_url
  - changeSet:
      id: dwy-order-35
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: need_upload
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 商家是否需要运营上传素材至平台（0:需要,1:不需要）
                  defaultValue: 1
              - column:
                  name: need_upload_link
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 需要上传的链接
              - column:
                  name: video_title
                  type: varchar(60)
                  constraints:
                    nullable: true
                  remarks: 视频标题
              - column:
                  name: video_cover_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 视频封面图 FK:resource.id
  - changeSet:
      id: dwy-order-36
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `need_upload` tinyint(1) NULL DEFAULT 1 COMMENT '商家是否需要运营上传素材至平台（0:需要,1:不需要）' AFTER `main_carry_count`,
            MODIFY COLUMN `need_upload_link` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '需要上传的链接' AFTER `need_upload`,
            MODIFY COLUMN `video_title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '视频标题' AFTER `need_upload_link`,
            MODIFY COLUMN `video_cover_id` bigint(20) NULL DEFAULT NULL COMMENT '视频封面图 FK:resource.id' AFTER `video_title`,
            MODIFY COLUMN `release_time` datetime NULL DEFAULT NULL COMMENT '订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）' AFTER `video_cover_id`;
  - changeSet:
      id: dwy-order-37
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: is_full_refund
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否全额退款（0:是,1:不是）
                  defaultValue: 1
  - changeSet:
      id: dwy-order-38
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_refund` 
            MODIFY COLUMN `is_full_refund` tinyint(1) NULL DEFAULT 1 COMMENT '是否全额退款（0:是,1:不是）' AFTER `reject_cause`;
  - changeSet:
      id: dwy-order-39
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: video_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 视频价格（单位：$）
              - column:
                  name: pic_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 图片费用（单位：$）
              - column:
                  name: exchange_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 手续费（单位：$）
              - column:
                  name: service_price
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 服务费（单位：$）
  - changeSet:
      id: dwy-order-40
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `service_price` decimal(12, 2) NOT NULL COMMENT '服务费（单位：$）' AFTER `exchange_price`,
            MODIFY COLUMN `create_order_user_id` bigint(20) NOT NULL COMMENT '创建订单用户id' AFTER `service_price`,
            MODIFY COLUMN `create_order_business_id` bigint(20) NOT NULL COMMENT '创建订单商家id' AFTER `create_order_user_id`,
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_order_business_id`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-41
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model
            columns:
              - column:
                  name: over_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 超时时间
  - changeSet:
      id: dwy-order-42
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model` 
            MODIFY COLUMN `over_time` datetime NULL DEFAULT NULL COMMENT '超时时间' AFTER `accept_time`;
  - changeSet:
      id: dwy-order-43
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model
            columns:
              - column:
                  name: is_refund
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 订单是否已全额退款（0:是,1:否）
                  afterColumn: over_time
                  defaultValue: 1
  - changeSet:
      id: dwy-order-44
      author: dwy
      changes:
        - dropColumn:
            columnName: is_refund
            tableName: order_video_model
  - changeSet:
      id: dwy-order-45
      author: dwy
      changes:
        - createTable:
            tableName: order_video_reminder_record
            isNotExists: true
            remarks: 订单_视频_催单记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频id (FK:order_video.id)
              - column:
                  name: video_code
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 视频编码
              - column:
                  name: product_pic
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 产品图（FK:resource.id）
              - column:
                  name: product_chinese
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 产品中文名
              - column:
                  name: product_english
                  type: varchar(80)
                  constraints:
                    nullable: false
                  remarks: 产品英文名
              - column:
                  name: create_order_business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单的商家id (FK:business.id)
              - column:
                  name: issue_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 出单人id
              - column:
                  name: reminder
                  type: int(10)
                  constraints:
                    nullable: false
                  remarks: 催单次数（累计）
              - column:
                  name: reminder_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 催单时间
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 催单状态（1:未处理,2:已确认,3:已完成）
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-46
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_reminder_record` 
            MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '催单状态（1:未处理,2:已确认,3:已完成）' AFTER `reminder_time`;
  - changeSet:
      id: dwy-order-47
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: un_finished_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单进入待完成的时间
                  afterColumn: un_confirm_time
  - changeSet:
      id: dwy-order-48
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: reminder_flag
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 催单标记（0:已处理,1:未处理）
                  afterColumn: un_finished_time
  - changeSet:
      id: dwy-order-49
      author: dwy
      changes:
        - dropColumn:
            columnName: reminder_flag
            tableName: order_video
  - changeSet:
      id: dwy-order-50
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video_reminder_record
            columns:
              - column:
                  name: issue_id
              - column:
                  name: create_order_business_id
              - column:
                  name: product_english
              - column:
                  name: product_chinese
              - column:
                  name: product_pic
              - column:
                  name: video_code
  - changeSet:
      id: dwy-order-51
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单商家id
                  afterColumn: create_order_user_id
  - changeSet:
      id: dwy-order-52
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `product_pic` varchar(64) NULL DEFAULT NULL COMMENT '产品图URI' AFTER `product_link`;
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `product_pic` varchar(64) NULL DEFAULT NULL COMMENT '产品图URI' AFTER `product_link`;
  - changeSet:
      id: dwy-order-53
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: Shipping_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 发货时间
                  afterColumn: reissue
  - changeSet:
      id: dwy-order-54
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            CHANGE COLUMN `Shipping_time` `shipping_time` datetime NULL DEFAULT NULL COMMENT '发货时间' AFTER `reissue`;
  - changeSet:
      id: dwy-order-55
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: status_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单进入新状态的时间
                  afterColumn: status
  - changeSet:
      id: dwy-order-56
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_content` 
            MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '类型（1:拍摄要求,2:匹配模特注意事项（原限制条件）,3:剪辑要求）' AFTER `target`;
  - changeSet:
      id: dwy-order-57
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_content_history` 
            MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '类型（1:拍摄要求,2:匹配模特注意事项（原限制条件）,3:剪辑要求）' AFTER `target`;
  - changeSet:
      id: dwy-order-58
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video
            columns:
              - column:
                  name: clips_required
  - changeSet:
      id: dwy-order-59
      author: dwy
      changes:
        - dropColumn:
            tableName: video_content
            columns:
              - column:
                  name: target
        - dropColumn:
            tableName: video_content_history
            columns:
              - column:
                  name: target
  - changeSet:
      id: dwy-order-60
      author: dwy
      changes:
        - createTable:
            tableName: order_resource
            isNotExists: true
            remarks: 订单_资源表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: object_key
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 资源URI
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-61
      author: dwy
      changes:
        - createIndex:
            tableName: order_resource
            indexName: uk_object_key
            unique: true
            columns:
              - column:
                  name: object_key
  - changeSet:
      id: dwy-order-62
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `reference_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参考图片（FK:order_resource.id，多个用,隔开）' AFTER `pic_count`;
  - changeSet:
      id: dwy-order-63
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `reference_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参考图片（FK:order_resource.id，多个用,隔开）' AFTER `pic_count`,
            MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注图片（FK:order_resource.id，多个用,隔开）' AFTER `shipping_remark`;
  - changeSet:
      id: dwy-order-64
      author: dwy
      changes:
        - dropIndex:
            tableName: order_resource
            indexName: uk_object_key
  - changeSet:
      id: dwy-order-65
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `resource_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '资源id（FK:order_resource.id，多个用,隔开）' AFTER `operator_time`;
  - changeSet:
      id: dwy-order-66
      author: dwy
      changes:
        - createTable:
            tableName: order_video_upload_link
            isNotExists: true
            remarks: 订单_视频_上传平台素材表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频id (FK:order_video.id)
              - column:
                  name: object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 上传对象（1:商家,2:运营）
              - column:
                  name: user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 上传用户id
              - column:
                  name: need_upload_link
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 需要上传的链接
              - column:
                  name: upload_link
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 最终上传的链接
              - column:
                  name: video_title
                  type: varchar(60)
                  constraints:
                    nullable: false
                  remarks: 视频标题
              - column:
                  name: video_cover
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 视频封面图URI
              - column:
                  name: remark
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 客服备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-67
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            CHANGE COLUMN `upload_link` `is_upload` tinyint(1) NULL DEFAULT 1 COMMENT '运营是否上传（0:已上传,1:未上传）' AFTER `need_upload_link`,
            MODIFY COLUMN `object` tinyint(1) NOT NULL COMMENT '提交信息对象（1:商家,2:运营）' AFTER `video_id`,
            MODIFY COLUMN `user_id` bigint(20) NOT NULL COMMENT '提交信息用户id' AFTER `object`,
            ADD COLUMN `time` datetime NOT NULL COMMENT '提交信息时间' AFTER `user_id`;
  - changeSet:
      id: dwy-order-68
      author: dwy
      changes:
        - addColumn:
            tableName: order_feed_back_material_info
            columns:
              - column:
                  name: object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 上传对象（1:模特,2:运营）
                  afterColumn: upload_time
              - sql: |
                  ALTER TABLE `order_feed_back_material_info` 
                  MODIFY COLUMN `material_id` bigint(20) NOT NULL COMMENT '素材id （FK:order_feed_back_material.id）' AFTER `id`;
  - changeSet:
      id: dwy-order-69
      author: dwy
      changes:
        - addColumn:
            tableName: order_feed_back_material_info
            columns:
              - column:
                  name: user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 上传用户id
                  afterColumn: object
        - sql: |
            ALTER TABLE `order_feed_back_material_info` 
            MODIFY COLUMN `material_id` bigint(20) NOT NULL COMMENT '素材id （FK:order_feed_back_material.id）' AFTER `id`;
  - changeSet:
      id: dwy-order-70
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_feed_back_material_info` 
            MODIFY COLUMN `object` tinyint(1) NOT NULL COMMENT '上传对象（2:运营,3:模特）' AFTER `upload_time`;
  - changeSet:
      id: dwy-order-71
      author: dwy
      changes:
        - addColumn:
            tableName: order_task
            columns:
              - column:
                  name: issue_pic
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 问题图片（FK:order_resource.id）
                  afterColumn: content
  - changeSet:
      id: dwy-order-72
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video
            columns:
              - column:
                  name: need_upload
              - column:
                  name: need_upload_link
              - column:
                  name: video_title
              - column:
                  name: video_cover_id
  - changeSet:
      id: dwy-order-73
      author: dwy
      changes:
        - createTable:
            tableName: order_video_change_log
            isNotExists: true
            remarks: 订单_视频_变更记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频id (FK:order_video.id)
              - column:
                  name: change_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 变更时间
              - column:
                  name: change_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 变更操作运营
              - column:
                  name: change_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 变更类型（预留字段）
              - column:
                  name: log_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 记录类型（1:初始记录,2:编辑订单时修改,3:商家同意后修改）
        - createTable:
            tableName: order_video_change_log_info
            isNotExists: true
            remarks: 订单_视频_变更记录_详情表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 1001
              - column:
                  name: change_log_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 变更记录id (FK:order_video_change_log.id)
              - column:
                  name: field_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 字段名称
              - column:
                  name: old_value
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 变更前的值
              - column:
                  name: new_value
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 变更后的值
  - changeSet:
      id: dwy-order-74
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_change_log_info` 
            MODIFY COLUMN `old_value` json NOT NULL COMMENT '变更前的值' AFTER `field_name`,
            MODIFY COLUMN `new_value` json NULL COMMENT '变更后的值' AFTER `old_value`,
            ADD COLUMN `field_type` varchar(30) NOT NULL COMMENT '字段类型' AFTER `new_value`;
  - changeSet:
      id: dwy-order-75
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_change_log_info` 
            MODIFY COLUMN `field_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段类型' AFTER `field_name`;
  - changeSet:
      id: dwy-order-76
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_change_log_info` 
            MODIFY COLUMN `old_value` json NULL COMMENT '变更前的值' AFTER `field_type`;
  - changeSet:
      id: dwy-order-77
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_change_log_info` 
            MODIFY COLUMN `field_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '字段类型' AFTER `field_name`;
  - changeSet:
      id: dwy-order-78
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video_change_log_info
            columns:
              - column:
                  name: old_value
              - column:
                  name: new_value
        - addColumn:
            tableName: order_video_change_log_info
            columns:
              - column:
                  name: value
                  type: json
                  constraints:
                    nullable: true
                  remarks: 当前值
                  afterColumn: field_type
  - changeSet:
      id: dwy-order-79
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_content` 
            MODIFY COLUMN `content` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容' AFTER `type`;
            ALTER TABLE `video_content_history` 
            MODIFY COLUMN `content` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容' AFTER `type`;
  - changeSet:
      id: dwy-order-80
      author: dwy
      changes:
        - addColumn:
            tableName: order_document_resource
            columns:
              - column:
                  name: object_key
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 图片资源URI
                  afterColumn: resource_id
  - changeSet:
      id: dwy-order-81
      author: dwy
      changes:
        - dropColumn:
            tableName: order_document_resource
            columns:
              - column:
                  name: resource_id
  - changeSet:
      id: dwy-order-82
      author: dwy
      changes:
      - createTable:
          tableName: order_video_flow
          isNotExists: true
          remarks: 订单_视频_流转记录表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: video_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 视频id (FK:order_video.id)
            - column:
                name: event_name
                type: varchar(20)
                constraints:
                  nullable: false
                remarks: 事件名称
            - column:
                name: event_execute_object
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 事件执行对象（1:商家,2:运营,3:模特,9:系统）
            - column:
                name: event_execute_user
                type: varchar(30)
                constraints:
                  nullable: false
                remarks: 事件执行人名称
            - column:
                name: event_execute_nick_name
                type: varchar(50)
                constraints:
                  nullable: true
                remarks: 事件执行人微信昵称
            - column:
                name: event_execute_phone
                type: varchar(15)
                constraints:
                  nullable: true
                remarks: 事件执行人手机号
            - column:
                name: event_execute_time
                type: datetime
                constraints:
                  nullable: false
                remarks: 事件执行时间
            - column:
                name: origin_status
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 原先视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
            - column:
                name: target_status
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
            - column:
                name: remark
                type: varchar(300)
                constraints:
                  nullable: true
                remarks: 备注
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-83
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_flow` 
          MODIFY COLUMN `event_execute_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '事件执行时间' AFTER `event_execute_phone`;
  - changeSet:
      id: dwy-order-84
      author: dwy
      changes:
      - dropTable:
          tableName: order_video_info_history
      - dropTable:
          tableName: video_content_history
  - changeSet:
      id: dwy-order-85
      author: dwy
      changes:
      - renameTable:
          oldTableName: video_content
          newTableName: order_video_content
  - changeSet:
      id: dwy-order-86
      author: dwy
      changes:
      - setTableRemarks:
          tableName: order_video_content
          remarks: 订单_视频_关联内容表
  - changeSet:
      id: dwy-order-87
      author: dwy
      changes:
        - setTableRemarks:
            tableName: order_task
            remarks: 订单_视频_工单任务表
        - renameTable:
            oldTableName: order_task
            newTableName: order_video_task
        - setTableRemarks:
            tableName: order_task_flow_record
            remarks: 订单_视频_工单任务_流转记录表
        - renameTable:
            oldTableName: order_task_flow_record
            newTableName: order_video_task_flow_record
  - changeSet:
      id: dwy-order-88
      author: dwy
      changes:
        - dropTable:
            tableName: order_video_remark
  - changeSet:
      id: dwy-order-89
      author: dwy
      changes:
        - setTableRemarks:
            tableName: order_feed_back
            remarks: 订单_视频_订单反馈表(商家)
        - renameTable:
            oldTableName: order_feed_back
            newTableName: order_video_feed_back
        - setTableRemarks:
            tableName: order_feed_back_material
            remarks: 订单_视频_订单反馈表(模特)
        - renameTable:
            oldTableName: order_feed_back_material
            newTableName: order_video_feed_back_material
        - setTableRemarks:
            tableName: order_feed_back_material_info
            remarks: 订单_视频_订单反馈表(模特)_详细信息
        - renameTable:
            oldTableName: order_feed_back_material_info
            newTableName: order_video_feed_back_material_info
  - changeSet:
      id: dwy-order-90
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_feed_back` 
          MODIFY COLUMN `video_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频订单id FK:order_video.id' AFTER `id`;
          ALTER TABLE `order_video_feed_back_material` 
          MODIFY COLUMN `video_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频订单id FK:order_video.id' AFTER `id`;
          ALTER TABLE `order_video_feed_back_material_info` 
          MODIFY COLUMN `material_id` bigint(20) NOT NULL COMMENT '素材id （FK:order_video_feed_back_material.id）' AFTER `id`;
  - changeSet:
      id: dwy-order-91
      author: dwy
      changes:
        - setTableRemarks:
            tableName: order_comment
            remarks: 订单_视频_订单备注表
        - renameTable:
            oldTableName: order_comment
            newTableName: order_video_comment
  - changeSet:
      id: dwy-order-92
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_feed_back` 
          MODIFY COLUMN `video_score_content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '评价内容' AFTER `video_score`;
  - changeSet:
      id: dwy-order-93
      author: dwy
      changes:
      - createTable:
          tableName: order_video_model_shipping_address
          isNotExists: true
          remarks: 订单_视频_模特收货信息表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: video_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 视频id (FK:order_video.id)
            - column:
                name: shoot_model_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 模特id (FK:order_video.shoot_model_id)
            - column:
                name: nation
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
            - column:
                name: recipient
                type: varchar(32)
                constraints:
                  nullable: false
                remarks: 收件人
            - column:
                name: city
                type: varchar(100)
                constraints:
                  nullable: false
                remarks: 城市
            - column:
                name: state
                type: varchar(100)
                constraints:
                  nullable: false
                remarks: 州
            - column:
                name: zipcode
                type: varchar(16)
                constraints:
                  nullable: false
                remarks: 邮编
            - column:
                name: detail_address
                type: varchar(100)
                constraints:
                  nullable: false
                remarks: 详细地址
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-94
      author: dwy
      changes:
      - addColumn:
          tableName: order_invoice
          columns:
            - column:
                name: invoicing_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 开票时间
                afterColumn: operator_time
            - column:
                name: number
                type: varchar(16)
                constraints:
                  nullable: true
                remarks: 发票号
                afterColumn: content
  - changeSet:
      id: dwy-order-95
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_invoice`
          CHANGE COLUMN `resource_id` `object_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票文件URI' AFTER `invoicing_time`;
  - changeSet:
      id: dwy-order-96
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_invoice`
          MODIFY COLUMN `invoicing_time` datetime NULL DEFAULT NULL COMMENT '开票时间' AFTER `status`;
  - changeSet:
      id: dwy-order-97
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_refund
          columns:
            - column:
                name: product_link
                type: varchar(1000)
                constraints:
                  nullable: true
                remarks: 产品链接
            - column:
                name: platform
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
            - column:
                name: shooting_country
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
            - column:
                name: pic_count
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 照片数量（1:2张/$10,2:5张/$20）
            - column:
                name: contact_id
                type: bigint(20)
                constraints:
                  nullable: true
                remarks: 对接人id
            - column:
                name: issue_id
                type: bigint(20)
                constraints:
                  nullable: true
                remarks: 出单人id
  - changeSet:
      id: dwy-order-98
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_refund`
            DROP COLUMN `merchant_id`,
            DROP COLUMN `merchant_code`,
            MODIFY COLUMN `apply_time` datetime NOT NULL COMMENT '申请时间' AFTER `video_id`,
            MODIFY COLUMN `video_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频编码' AFTER `refund_num`,
            MODIFY COLUMN `product_chinese` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品中文名' AFTER `video_code`,
            MODIFY COLUMN `product_english` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`,
            MODIFY COLUMN `product_link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品链接' AFTER `product_english`,
            MODIFY COLUMN `platform` tinyint(1) NOT NULL COMMENT '使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `product_link`,
            MODIFY COLUMN `shooting_country` tinyint(1) NOT NULL COMMENT '拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）' AFTER `platform`,
            MODIFY COLUMN `pic_count` tinyint(1) NULL DEFAULT NULL COMMENT '照片数量（1:2张/$10,2:5张/$20）' AFTER `shooting_country`,
            MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成）' AFTER `pic_count`,
            MODIFY COLUMN `shoot_model_id` bigint NULL DEFAULT NULL COMMENT '拍摄模特id' AFTER `status`,
            MODIFY COLUMN `contact_id` bigint NOT NULL COMMENT '对接人id' AFTER `shoot_model_id`,
            MODIFY COLUMN `issue_id` bigint NULL DEFAULT NULL COMMENT '出单人id' AFTER `contact_id`,
            MODIFY COLUMN `amount` decimal(12, 2) NOT NULL COMMENT '视频金额（单位：￥）' AFTER `issue_id`,
            MODIFY COLUMN `refund_amount` decimal(12, 2) NOT NULL COMMENT '退款金额（单位：￥）' AFTER `amount`,
            MODIFY COLUMN `refund_type` tinyint(1) NOT NULL COMMENT '退款类型（1:补偿,2:取消订单,3:取消选配）' AFTER `refund_amount`,
            MODIFY COLUMN `initiator_source` tinyint(1) NOT NULL COMMENT '发起方（1:商家,2:平台）' AFTER `refund_type`,
            MODIFY COLUMN `initiator_id` bigint NOT NULL COMMENT '发起人id' AFTER `initiator_source`,
            MODIFY COLUMN `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）' AFTER `refund_cause`;
  - changeSet:
      id: dwy-order-99
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_refund
          columns:
            - column:
                name: business_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 商家id
                afterColumn: reject_cause
  - changeSet:
      id: dwy-order-100
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_flow`
          CHANGE COLUMN `event_execute_user` `event_execute_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件执行人用户名称' AFTER `event_execute_object`,
          ADD COLUMN `event_execute_user_id` bigint NULL COMMENT '事件执行人用户id' AFTER `event_execute_object`;
  - changeSet:
      id: dwy-order-100
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_flow` 
            CHANGE COLUMN `event_execute_user` `event_execute_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件执行人用户名称' AFTER `event_execute_object`,
            ADD COLUMN `event_execute_user_id` bigint NULL COMMENT '事件执行人用户id' AFTER `event_execute_object`;
  - changeSet:
      id: dwy-order-101
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_feed_back_material_info`
          MODIFY COLUMN `link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '链接' AFTER `material_id`;
  - changeSet:
      id: dwy-order-102
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_feed_back`
          MODIFY COLUMN `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '成品url' AFTER `video_id`;
  - changeSet:
      id: dwy-order-103
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_upload_link`
          MODIFY COLUMN `need_upload_link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '需要上传的链接' AFTER `time`,
          MODIFY COLUMN `remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客服备注' AFTER `video_cover`;
  - changeSet:
      id: dwy-order-104
      author: dwy
      changes:
      - modifyDataType:
          tableName: video_cart_content
          columnName: content
          newDataType: varchar(800)
  - changeSet:
      id: dwy-order-105
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `video_cart_content`
          MODIFY COLUMN `content` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容' AFTER `video_cart_id`;
  - changeSet:
      id: dwy-order-106
      author: dwy
      changes:
      - setColumnRemarks:
          tableName: order_video_task_flow_record
          columnName: remark
          remarks: 备注
          columnDataType: varchar(32)
  - changeSet:
      id: dwy-order-107
      author: dwy
      changes:
      - addNotNullConstraint:
          tableName: order_video_task_flow_record
          columnName: operate_type
          defaultNullValue: 1
          columnDataType: tinyint(1)
  - changeSet:
      id: dwy-order-108
      author: dwy
      changes:
      - setColumnRemarks:
          tableName: order_video_task_flow_record
          columnName: operate_type
          remarks: 操作类型（1:创建工单,2:取消工单,3:重新打开,4:拒绝工单,5:标记处理中,6:标记已处理）
          columnDataType: tinyint(1)
  - changeSet:
      id: dwy-order-109
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_task_flow_record`
          MODIFY COLUMN `time` datetime NOT NULL COMMENT '记录时间' AFTER `task_num`,
          MODIFY COLUMN `assignee_id` bigint NOT NULL COMMENT '处理人id' AFTER `time`,
          MODIFY COLUMN `operate_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '操作类型（1:创建工单,2:取消工单,3:重新打开,4:拒绝工单,5:标记处理中,6:标记已处理）' AFTER `assignee_id`;
  - changeSet:
      id: dwy-order-110
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_task`
          MODIFY COLUMN `content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工单内容' AFTER `type`,
          MODIFY COLUMN `submit_by` bigint NOT NULL COMMENT '提交人id' AFTER `issue_pic`,
          MODIFY COLUMN `submit_time` datetime NOT NULL COMMENT '提交时间' AFTER `submit_by`,
          MODIFY COLUMN `video_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频编码' AFTER `submit_time`,
          MODIFY COLUMN `product_chinese` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品中文名' AFTER `video_code`,
          MODIFY COLUMN `product_english` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`,
          MODIFY COLUMN `shoot_model_id` bigint NOT NULL COMMENT '拍摄模特id' AFTER `product_english`,
          MODIFY COLUMN `order_user_id` bigint NOT NULL COMMENT '下单用户id' AFTER `shoot_model_id`,
          MODIFY COLUMN `priority` tinyint(1) NOT NULL COMMENT '优先级（1:紧急,2:一般）' AFTER `order_user_id`,
          MODIFY COLUMN `assignee_id` bigint NOT NULL COMMENT '处理人id' AFTER `priority`,
          MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '工单状态（1:待处理,2:处理中,3:已处理,4:已拒绝,5:已关闭）' AFTER `assignee_id`;
  - changeSet:
      id: dwy-order-111
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_tag`
            MODIFY COLUMN `video_id` bigint NOT NULL COMMENT '视频id' AFTER `id`,
            MODIFY COLUMN `tag_id` bigint NOT NULL COMMENT '标签id' AFTER `video_id`,
            MODIFY COLUMN `category_id` bigint NOT NULL COMMENT '标签分类id' AFTER `tag_id`;
  - changeSet:
      id: dwy-order-112
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_refund`
          MODIFY COLUMN `refund_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '退款单号' AFTER `order_num`,
          MODIFY COLUMN `video_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频编码' AFTER `refund_num`,
          MODIFY COLUMN `product_english` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`,
          MODIFY COLUMN `refund_cause` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '退款原因' AFTER `initiator_id`,
          MODIFY COLUMN `reject_cause` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '拒绝理由' AFTER `operate_time`;
  - changeSet:
      id: dwy-order-113
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_preselect_model`
            MODIFY COLUMN `add_type` tinyint(1) NOT NULL COMMENT '添加方式（1:意向模特,2:模特自选,3:运营添加）' AFTER `video_id`,
            MODIFY COLUMN `add_time` datetime NOT NULL COMMENT '添加时间' AFTER `add_user_id`,
            MODIFY COLUMN `model_id` bigint NOT NULL COMMENT '模特id' AFTER `add_time`,
            MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0:未对接,1:已对接,2:已选定,3:已淘汰）' AFTER `model_id`,
            MODIFY COLUMN `remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注' AFTER `status`;
  - changeSet:
      id: dwy-order-114
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_model_shipping_address`
          MODIFY COLUMN `state` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '州' AFTER `city`;
  - changeSet:
      id: dwy-order-115
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_model`
          MODIFY COLUMN `model_id` bigint NOT NULL COMMENT '模特id' AFTER `id`,
          MODIFY COLUMN `video_id` bigint NOT NULL COMMENT '视频id' AFTER `model_id`;
  - changeSet:
      id: dwy-order-116
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_logistic`
          MODIFY COLUMN `shipping_time` datetime NOT NULL COMMENT '发货时间' AFTER `reissue`;
  - changeSet:
      id: dwy-order-117
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info`
            MODIFY COLUMN `upload_time` datetime NOT NULL COMMENT '上传时间' AFTER `note`;
  - changeSet:
      id: dwy-order-118
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back`
            MODIFY COLUMN `video_id` bigint NOT NULL COMMENT '视频订单id FK:order_video.id' AFTER `id`,
            MODIFY COLUMN `video_score` tinyint(1) NOT NULL COMMENT '视频评分' AFTER `url`;
  - changeSet:
      id: dwy-order-119
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_feed_back_material`
          MODIFY COLUMN `video_id` bigint NOT NULL COMMENT '视频订单id FK:order_video.id' AFTER `id`;
  - changeSet:
      id: dwy-order-120
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_video_comment`
          MODIFY COLUMN `video_id` bigint NOT NULL COMMENT '视频订单id' AFTER `id`;
  - changeSet:
      id: dwy-order-121
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video_case
            columns:
              - column:
                  name: create_by
              - column:
                  name: update_by
  - changeSet:
      id: dwy-order-122
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video`
            MODIFY COLUMN `product_pic` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品图URI' AFTER `order_num`,
            MODIFY COLUMN `video_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '视频编码' AFTER `product_pic`,
            MODIFY COLUMN `product_english` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`,
            MODIFY COLUMN `platform` tinyint(1) NOT NULL COMMENT '使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `reference_video_link`,
            MODIFY COLUMN `is_object` tinyint(1) NOT NULL COMMENT '是否需要发货（0:需要,1:不需要）' AFTER `platform`,
            MODIFY COLUMN `create_order_business_id` bigint NOT NULL COMMENT '创建订单商家id' AFTER `reference_pic`,
            MODIFY COLUMN `create_order_user_id` bigint NOT NULL COMMENT '创建订单用户id' AFTER `create_order_business_id`,
            MODIFY COLUMN `contact_id` bigint NULL DEFAULT NULL COMMENT '对接人id' AFTER `shoot_model_id`,
            MODIFY COLUMN `issue_id` bigint NULL DEFAULT NULL COMMENT '出单人id' AFTER `contact_id`,
            MODIFY COLUMN `status` tinyint NOT NULL COMMENT '订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）' AFTER `issue_id`,
            MODIFY COLUMN `status_time` datetime NOT NULL COMMENT '订单进入新状态的时间' AFTER `status`,
            MODIFY COLUMN `video_duration` int NULL DEFAULT NULL COMMENT '视频时长（单位：秒）' AFTER `service_price`,
            MODIFY COLUMN `schedule_type` tinyint(1) NULL DEFAULT NULL COMMENT '排单类型（1:排单,2:携带排单）' AFTER `commission`,
            MODIFY COLUMN `shipping_remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注' AFTER `main_carry_count`,
            MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注图片（FK:order_resource.id，多个用,隔开）' AFTER `shipping_remark`,
            MODIFY COLUMN `logistic_flag` tinyint(1) NULL DEFAULT NULL COMMENT '标记物流状态（1:标记发货）' AFTER `shipping_pic`,
            MODIFY COLUMN `is_care` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否照顾单（0=否,1=是）' AFTER `logistic_flag`,
            MODIFY COLUMN `release_time` datetime NULL DEFAULT NULL COMMENT '订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）' AFTER `un_finished_time`,
            MODIFY COLUMN `release_flag` tinyint(1) NULL DEFAULT 0 COMMENT '订单完全释放flag（1:表示视频订单不需要再过滤24小时，可以放到模特订单公池中）' AFTER `release_time`;
  - changeSet:
      id: dwy-order-123
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table`
            MODIFY COLUMN `current_exchange_rate` decimal(12, 4) NOT NULL COMMENT '当前汇率' AFTER `real_pay_amount`,
            MODIFY COLUMN `pay_type` tinyint NULL COMMENT '支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付）' AFTER `pay_account`,
            MODIFY COLUMN `merchant_id` bigint NOT NULL COMMENT '商家id  FK  business.id' AFTER `order_remark`,
            MODIFY COLUMN `merchant_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '商家会员编码  FK  business.member_code' AFTER `merchant_id`;
  - changeSet:
      id: dwy-order-124
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_note`
            MODIFY COLUMN `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票抬头' AFTER `order_num`,
            MODIFY COLUMN `duty_paragraph` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '税号' AFTER `title`,
            MODIFY COLUMN `content` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票内容' AFTER `duty_paragraph`;
  - changeSet:
      id: dwy-order-125
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_invoice`
          MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号' AFTER `id`,
          MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '订单类型（0:视频订单,1:会员订单）' AFTER `order_num`,
          MODIFY COLUMN `pay_time` datetime NOT NULL COMMENT '支付时间' AFTER `type`,
          MODIFY COLUMN `merchant_id` bigint NOT NULL COMMENT '商家id' AFTER `pay_time`,
          MODIFY COLUMN `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票抬头' AFTER `merchant_id`,
          MODIFY COLUMN `duty_paragraph` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '税号' AFTER `title`,
          MODIFY COLUMN `content` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票内容' AFTER `duty_paragraph`,
          MODIFY COLUMN `amount` decimal(12, 2) NOT NULL COMMENT '开票金额' AFTER `number`,
          MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '开票状态（1:待开票,2:待确认,3:已投递,4:已作废）' AFTER `amount`;
  - changeSet:
      id: dwy-order-126
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart`
            MODIFY COLUMN `product_pic` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '产品图URI' AFTER `id`,
            MODIFY COLUMN `platform` tinyint(1) NOT NULL COMMENT '使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `reference_video_link`,
            MODIFY COLUMN `video_format` tinyint(1) NOT NULL COMMENT '视频格式（1:横屏16：9,2:竖屏9：16）' AFTER `platform`,
            MODIFY COLUMN `shooting_country` tinyint(1) NOT NULL COMMENT '拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）' AFTER `video_format`,
            MODIFY COLUMN `model_type` tinyint(1) NOT NULL COMMENT '模特类型（0:影响者,1:素人）' AFTER `shooting_country`,
            MODIFY COLUMN `reference_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参考图片（FK:order_resource.id，多个用,隔开）' AFTER `pic_count`,
            MODIFY COLUMN `create_order_business_id` bigint NOT NULL COMMENT '创建订单商家id' AFTER `reference_pic`,
            MODIFY COLUMN `create_order_user_id` bigint NOT NULL COMMENT '创建订单用户id' AFTER `create_order_business_id`,
            MODIFY COLUMN `intention_model_id` bigint NULL DEFAULT NULL COMMENT '意向模特id' AFTER `create_order_user_id`;
  - changeSet:
      id: dwy-order-127
      author: dwy
      changes:
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id FK:order_video.id
            tableName: order_video_task
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_code
            remarks: 视频编码
            tableName: order_video_task
            columnDataType: varchar(10)
        - setColumnRemarks:
            columnName: product_english
            remarks: 产品英文名
            tableName: order_video_task
            columnDataType: varchar(80)
  - changeSet:
      id: dwy-order-128
      author: dwy
      changes:
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_tag
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_refund
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_preselect_model
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_model_select
            columnDataType: bigint
        - setColumnRemarks:
            columnName: model_id
            remarks: 模特id (FK:model.id)
            tableName: order_video_model_search
            columnDataType: bigint
        - setColumnRemarks:
            columnName: model_id
            remarks: 模特id (FK:model.id)
            tableName: order_video_model
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_model
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_logistic
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_content
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_comment
            columnDataType: bigint
        - setColumnRemarks:
            columnName: video_id
            remarks: 视频订单id (FK:order_video.id)
            tableName: order_video_case
            columnDataType: bigint
        - setColumnRemarks:
            columnName: model_id
            remarks: 模特id (FK:model.id)
            tableName: order_video_carry
            columnDataType: bigint
        - setColumnRemarks:
            columnName: main_carry_video_id
            remarks: 主携带视频订单id (FK:order_video.id)
            tableName: order_video_carry
            columnDataType: bigint
        - setColumnRemarks:
            columnName: carry_video_id
            remarks: 被携带视频订单id (FK:order_video.id)
            tableName: order_video_carry
            columnDataType: bigint
        - setColumnRemarks:
            columnName: order_num
            remarks: 订单编号 (FK:order_table.order_num)
            tableName: order_note
            columnDataType: varchar(30)
        - setColumnRemarks:
            columnName: order_num
            remarks: 订单编号 (FK:order_table.order_num)
            tableName: order_invoice
            columnDataType: varchar(30)
  - changeSet:
      id: dwy-order-129
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: create_order_user_account
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单用户账号
                  afterColumn: create_order_user_id
              - column:
                  name: create_order_business_account
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单商家账号
                  afterColumn: create_order_business_id
  - changeSet:
      id: dwy-order-130
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: create_order_business_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单商家名称
                  afterColumn: create_order_business_account
              - column:
                  name: create_order_user_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 创建订单用户名称
                  afterColumn: create_order_user_account
  - changeSet:
      id: dwy-order-131
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            DROP COLUMN `create_order_business_account`,
            DROP COLUMN `create_order_business_name`,
            MODIFY COLUMN `create_order_user_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '创建订单用户名称' AFTER `create_order_user_account`;
      comment: 删除多余的字段以及create_order_user_name设置可以为空
  - changeSet:
      id: dwy-order-132
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: create_order_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 创建订单用户微信名称
                  afterColumn: create_order_user_name
  - changeSet:
      id: dwy-order-133
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `create_order_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建订单用户名称' AFTER `create_order_user_account`;
      comment: 修改create_order_user_name字段字符长度为32
  - changeSet:
      id: dwy-order-134
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 创建订单登录用户id
                  afterColumn: create_order_business_id
  - changeSet:
      id: dwy-order-135
      author: dwy
      changes:
        - dropColumn:
            tableName: video_cart
            columns:
              - column:
                  name: biz_user_id
  - changeSet:
      id: dwy-order-136
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: create_order_biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 创建订单登录用户id
                  afterColumn: create_order_business_id
  - changeSet:
      id: dwy-order-137
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 创建订单登录用户id
                  afterColumn: create_order_business_id
  - changeSet:
      id: dwy-order-138
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_user_account
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 创建订单用户账号
                  afterColumn: create_order_user_id
  - changeSet:
      id: dwy-order-139
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_user_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户名称
                  afterColumn: create_order_user_account
              - column:
                  name: create_order_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 创建订单用户微信名称
  - changeSet:
      id: dwy-order-140
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `create_order_user_nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建订单用户微信名称' AFTER `create_order_user_name`;
      comment: 修改create_order_user_nick_name排序
  - changeSet:
      id: dwy-order-141
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_refund` 
            CHANGE COLUMN `initiator_id` `initiator_name` varchar(32) NOT NULL COMMENT '发起人名称' AFTER `initiator_source`;
      comment: 修改initiator_id字段为initiator_name
  - changeSet:
      id: dwy-order-142
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `create_order_user_account` varchar(20) NOT NULL COMMENT '创建订单用户账号' AFTER `create_order_user_id`;
      comment: 修改create_order_user_account字段为varchar
  - changeSet:
      id: dwy-order-143
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: seed_code
                  type: varchar(4)
                  constraints:
                    nullable: true
                  remarks: 种草码
                  afterColumn: tax_point_cost
  - changeSet:
      id: dwy-order-144
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: seed_code_discount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 种草码优惠金额
                  afterColumn: seed_code
  - changeSet:
      id: dwy-order-145
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `seed_code_discount` decimal(12, 2) NULL DEFAULT NULL COMMENT '种草码优惠金额（单位：￥）' AFTER `seed_code`
      comment: seed_code_discount字段为备注修改
  - changeSet:
      id: dwy-order-146
      author: dwy
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: real_pay_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 订单实付金额（单位：￥）
                  afterColumn: member_package_type
              - column:
                  name: operation_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 操作时间（当前需求只有一次操作，即运营点击结算的时间）
                  afterColumn: create_time
  - changeSet:
      id: dwy-order-147
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_member_channel` 
          MODIFY COLUMN `settle_user_id` bigint NULL COMMENT '结算人id（sys_user.user_id）' AFTER `settle_user_name`
      comment: order_member_channel.settle_user_id 取消非空限制
  - changeSet:
      id: dwy-order-148
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: back_modify_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 运营修改的订单金额（单位：￥） 正数则为增加金额，负数则为减少金额
                  afterColumn: seed_code_discount
  - changeSet:
      id: dwy-order-149
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `seed_code_discount` decimal(12, 2) NULL DEFAULT 0.0 COMMENT '种草码优惠金额（单位：￥）' AFTER `seed_code`,
            MODIFY COLUMN `back_modify_amount` decimal(12, 2) NULL DEFAULT 0.0 COMMENT '运营修改的订单金额（单位：￥） 正数则为增加金额，负数则为减少金额' AFTER `seed_code_discount`;
      comment: seed_code_discount和back_modify_amount 设置默认值0.0
  - changeSet:
      id: dwy-order-150
      author: dwy
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: biz_user_id
                  type: bigint
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户id FK:biz_user.id
                  afterColumn: business_name
  - changeSet:
      id: dwy-order-151
      author: dwy
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: biz_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户微信昵称
                  afterColumn: biz_user_id
  - changeSet:
      id: dwy-order-152
      author: dwy
      changes:
        - addColumn:
            tableName: order_member_channel
            columns:
              - column:
                  name: biz_user_phone
                  type: varchar(15)
                  constraints:
                    nullable: false
                  remarks: 商家端登录用户手机号
                  afterColumn: biz_user_nick_name
  - changeSet:
      id: dwy-order-153
      author: dwy
      changes:
      - sql: |
          ALTER TABLE `order_table` 
          MODIFY COLUMN `pay_amount` decimal(12, 2) NOT NULL COMMENT '需支付金额（单位：￥）' AFTER `order_amount`;
      comment: pay_amount 备注修改 添加非空条件
  - changeSet:
      id: dwy-order-154
      author: dwy
      changes:
      - createTable:
          tableName: order_video_back_modify_amount_records
          isNotExists: true
          remarks: 订单_视频_运营修改视频价格记录表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: video_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 视频id (FK:order_video.id)
            - column:
                name: old_video_price
                type: decimal(12,2)
                constraints:
                  nullable: false
                remarks: 原视频价格（单位：$）
            - column:
                name: new_video_price
                type: decimal(12,2)
                constraints:
                  nullable: false
                remarks: 新视频价格（单位：$）
            - column:
                name: create_by
                type: varchar(32)
                constraints:
                  nullable: false
                remarks: 创建人名称
            - column:
                name: create_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 创建人id
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-155
      author: dwy
      changes:
      - addDefaultValue:
          columnDataType:  decimal(12,2)
          columnName:  tax_point_cost
          defaultValue:  0.00
          tableName:  order_table
  - changeSet:
      id: dwy-order-156
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_refund` 
            MODIFY COLUMN `refund_cause` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '退款原因' AFTER `initiator_name`;
      comment: order_video_refund.refund_cause 取消非空限制
  - changeSet:
      id: dwy-order-157
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_flow
          columns:
            - column:
                name: event_content
                type: varchar(300)
                constraints:
                  nullable: false
                remarks: 事件内容
                afterColumn: event_execute_time
  - changeSet:
      id: dwy-order-158
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_flow` 
            MODIFY COLUMN `target_status` tinyint(1) NULL COMMENT '目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）' AFTER `origin_status`;
      comment: target_status 取消非空限制
  - changeSet:
      id: dwy-order-159
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_flow
          columns:
            - column:
                name: is_cart
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 是否是购物车(1:是)
                afterColumn: event_content
  - changeSet:
      id: dwy-order-160
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_flow
          columns:
            - column:
                name: is_public
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 是否公开（0:公开,1:只显示给运营）
                defaultValue: 0
                afterColumn: is_cart
  - changeSet:
      id: dwy-order-161
      author: dwy
      changes:
        - createTable:
            tableName: order_video_operate
            isNotExists: true
            remarks: 订单_视频_操作记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频id (FK:order_video.id)
              - column:
                  name: event_name
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 事件名称
              - column:
                  name: event_execute_object
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 事件执行对象（1:商家,2:运营,3:模特,9:系统）
              - column:
                  name: event_execute_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 事件执行人用户id
              - column:
                  name: event_execute_user_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 事件执行人用户名称
              - column:
                  name: event_execute_nick_name
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 事件执行人微信昵称
              - column:
                  name: event_execute_phone
                  type: varchar(15)
                  constraints:
                    nullable: true
                  remarks: 事件执行人手机号
              - column:
                  name: event_execute_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 事件执行时间
              - column:
                  name: event_content
                  type: varchar(300)
                  constraints:
                    nullable: false
                  remarks: 事件内容
              - column:
                  name: is_cart
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否是购物车(1:是)
                  defaultValue: 0
              - column:
                  name: is_public
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否公开（0:公开,1:只显示给运营）
                  defaultValue: 0
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-162
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_flow` 
            DROP COLUMN `event_content`,
            DROP COLUMN `is_cart`,
            DROP COLUMN `is_public`,
            MODIFY COLUMN `target_status` tinyint(1) NOT NULL COMMENT '目标视频订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）' AFTER `origin_status`;
      comment: 删除event_content、is_cart、is_public；  target_status回滚为之前非空限制
  - changeSet:
      id: dwy-order-163
      author: dwy
      changes:
      - createTable:
          tableName: order_video_flow_node_diagram
          isNotExists: true
          remarks: 订单_视频_流转节点图表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: video_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 视频id (FK:order_video.id)
            - column:
                name: node
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 当前节点（1:下单支付,2:匹配模特,3:商家发货,4:完成拍摄,5:商家确认,6:订单完成,10:取消订单）
            - column:
                name: time
                type: datetime
                constraints:
                  nullable: true
                remarks: 节点完成时间
  - changeSet:
      id: dwy-order-164
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_flow_node_diagram
          columns:
            - column:
                name: sort
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 排序
                defaultValue: 0
                afterColumn: time
  - changeSet:
      id: dwy-order-165
      author: dwy
      changes:
      - createTable:
          tableName: order_video_model_change
          isNotExists: true
          remarks: 订单_视频_模特变更记录表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: video_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 视频id (FK:order_video.id)
            - column:
                name: model_pic
                type: varchar(64)
                constraints:
                  nullable: false
                remarks: 模特头图URI
            - column:
                name: name
                type: varchar(16)
                constraints:
                  nullable: false
                remarks: 模特姓名
            - column:
                name: nation
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
            - column:
                name: sex
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 性别(1:男,0:女)
            - column:
                name: type
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 模特类型(0:影响者,1:素人)
            - column:
                name: age_group
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）
            - column:
                name: platform
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
            - column:
                name: cooperation
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 合作深度(0:一般模特,1:优质模特,2:中等模特)
            - column:
                name: issue_user_name
                type: varchar(30)
                constraints:
                  nullable: false
                remarks: 英文部客服名称
            - column:
                name: selected_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 选定时间
            - column:
                name: schedule_type
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 排单类型（1:排单,2:携带排单）
            - column:
                name: carry_type
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 携带类型（1:主携带,2:被携带）
            - column:
                name: commission_unit
                type: varchar(10)
                constraints:
                  nullable: true
                remarks: 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
            - column:
                name: commission
                type: decimal(12,2)
                constraints:
                  nullable: true
                remarks: 模特佣金
            - column:
                name: specialty_category
                type: varchar(1000)
                constraints:
                  nullable: true
                remarks: 擅长品类
            - column:
                name: model_tag
                type: varchar(1000)
                constraints:
                  nullable: true
                remarks: 模特标签
  - changeSet:
      id: dwy-order-166
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_model_change
          columns:
            - column:
                name: model_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 模特id
                afterColumn: video_id
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                afterColumn: model_tag
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-167
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_change
            columns:
              - column:
                  name: source
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 模特来源（1:意向模特,2:拍摄模特）
                  afterColumn: model_id
  - changeSet:
      id: dwy-order-168
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_change` 
            MODIFY COLUMN `platform` varchar(10) NOT NULL COMMENT '平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `age_group`;
      comments: platform更换为varchar
  - changeSet:
      id: dwy-order-169
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: auto_complete_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频订单自动完成时间
                  afterColumn: un_finished_time
  - changeSet:
      id: dwy-order-170
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: last_change_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频订单最后变更时间
                  afterColumn: auto_complete_time
  - changeSet:
      id: dwy-order-171
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: upload_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 上传的链接
                  afterColumn: video_cover
  - changeSet:
      id: dwy-order-172
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: upload_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 上传用户id
                  afterColumn: upload_link
  - changeSet:
      id: dwy-order-173
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: upload_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 上传时间
                  afterColumn: upload_user_id
  - changeSet:
      id: dwy-order-174
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: upload_user_name
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 上传用户名称
                  afterColumn: upload_user_id
  - changeSet:
      id: dwy-order-175
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            MODIFY COLUMN `upload_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '上传用户名称' AFTER `upload_user_id`;
      comments: upload_user_name 取消非空限制
  - changeSet:
      id: dwy-order-176
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_roast` 
            MODIFY COLUMN `object` tinyint(1) NOT NULL COMMENT '吐槽对象(1:视频,2:客服,3:其他)' AFTER `video_id`;
      comments: object修改为非空 补充注释
  - changeSet:
      id: dwy-order-177
      author: dwy
      changes:
      - createTable:
          tableName: alipay_order_table
          isNotExists: true
          remarks: 支付宝订单表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: mchnt_order_no
                type: varchar(30)
                constraints:
                  nullable: false
                remarks: 商户订单号
            - column:
                name: order_num
                type: varchar(30)
                constraints:
                  nullable: false
                remarks: 内部订单号
            - column:
                name: qrcode
                type: varchar(100)
                constraints:
                  nullable: false
                remarks: 二维码
            - column:
                name: order_amount
                type: decimal(12,2)
                constraints:
                  nullable: false
                remarks: 订单金额
            - column:
                name: status
                type: tinyint(1)
                constraints:
                  nullable: true
                remarks: 是否有效（1-有效， 0-无效）
                defaultValue: 1
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-178
      author: dwy
      changes:
      - createTable:
          tableName: alipay_pay_log
          isNotExists: true
          remarks: 支付宝回调记录表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: notify_time
                type: datetime
                constraints:
                  nullable: false
                remarks: 通知的发送时间
            - column:
                name: notify_type
                type: varchar(64)
                constraints:
                  nullable: false
                remarks: 通知类型
            - column:
                name: notify_id
                type: varchar(128)
                constraints:
                  nullable: false
                remarks: 通知校验 ID
            - column:
                name: charset
                type: varchar(10)
                constraints:
                  nullable: false
                remarks: 编码格式。如 utf-8、gbk、gb312等。
            - column:
                name: version
                type: varchar(3)
                constraints:
                  nullable: false
                remarks: 调用的接口版本。固定为1.0
            - column:
                name: sign_type
                type: varchar(10)
                constraints:
                  nullable: false
                remarks: 签名类型。签名算法类型，目前支持RSA2和RSA，推荐使用 RSA2
            - column:
                name: sign
                type: varchar(344)
                constraints:
                  nullable: false
                remarks: 签名。详情可查看 [异步返回结果的验签]
            - column:
                name: auth_app_id
                type: varchar(32)
                constraints:
                  nullable: false
                remarks: 授权方的APPID。由于本接口暂不开放第三方应用授权，因此 auth_app_id=app_id
            - column:
                name: trade_no
                type: varchar(64)
                constraints:
                  nullable: false
                remarks: 支付宝交易号，支付宝交易凭证号。
            - column:
                name: app_id
                type: varchar(32)
                constraints:
                  nullable: false
                remarks: 支付宝应用的APPID。支付宝分配给开发者的应用 ID
            - column:
                name: out_trade_no
                type: varchar(64)
                constraints:
                  nullable: false
                remarks: 商家订单号。原支付请求的商家订单号
            - column:
                name: out_biz_no
                type: varchar(64)
                constraints:
                  nullable: true
                remarks: 商家业务号。商家业务ID，通常是退款通知中返回的退款申请流水号
            - column:
                name: buyer_id
                type: varchar(128)
                constraints:
                  nullable: true
                remarks: 买家支付宝用户号。买家支付宝账号对应的支付宝唯一用户号。新商户建议使用open_id替代该字段。对于新商户，user_id字段未来计划逐步回收，存量商户可继续使用。如使用open_id，请确认 应用-开发配置-openid配置管理 已启用。无该配置项，可查看[openid配置申请]。
            - column:
                name: seller_id
                type: varchar(30)
                constraints:
                  nullable: true
                remarks: 卖家支付宝账号 ID。以 2088 开头的纯 16 位数字
            - column:
                name: trade_status
                type: varchar(32)
                constraints:
                  nullable: true
                remarks: 交易状态。交易目前所处状态，详情可查看下表 [交易状态说明]（WAIT_BUYER_PAY:交易创建，等待买家付款；TRADE_CLOSED:未付款交易超时关闭，或支付完成后全额退款；TRADE_SUCCESS:交易支付成功；TRADE_FINISHED:交易结束，不可退款）
            - column:
                name: total_amount
                type: decimal(13,2)
                constraints:
                  nullable: true
                remarks: 订单金额。本次交易支付订单金额，单位为人民币（元），精确到小数点后 2 位
            - column:
                name: receipt_amount
                type: decimal(13,2)
                constraints:
                  nullable: true
                remarks: 实收金额。商家在交易中实际收到的款项，单位为人民币（元），精确到小数点后 2 位
            - column:
                name: invoice_amount
                type: decimal(13,2)
                constraints:
                  nullable: true
                remarks: 开票金额。用户在交易中支付的可开发票的金额，单位为人民币（元），精确到小数点后 2 位
            - column:
                name: buyer_pay_amount
                type: decimal(13,2)
                constraints:
                  nullable: true
                remarks: 用户在交易中支付的金额，单位为人民币（元），精确到小数点后 2 位
            - column:
                name: point_amount
                type: decimal(13,2)
                constraints:
                  nullable: true
                remarks: 使用集分宝支付金额，单位为人民币（元），精确到小数点后 2 位
            - column:
                name: refund_fee
                type: decimal(13,2)
                constraints:
                  nullable: true
                remarks: 总退款金额。退款通知中，返回总退款金额，单位为人民币（元），精确到小数点后 2 位
            - column:
                name: subject
                type: varchar(256)
                constraints:
                  nullable: true
                remarks: 订单标题/商品标题/交易标题/订单关键字等，是请求时对应参数，会在通知中原样传回
            - column:
                name: body
                type: varchar(400)
                constraints:
                  nullable: true
                remarks: 商品描述。该订单的备注、描述、明细等。对应请求时的 body 参数，会在通知中原样传回
            - column:
                name: gmt_create
                type: datetime
                constraints:
                  nullable: true
                remarks: 交易创建时间。格式为 yyyy-MM-dd HH:mm:ss
            - column:
                name: gmt_payment
                type: datetime
                constraints:
                  nullable: true
                remarks: 交易付款时间。格式为 yyyy-MM-dd HH:mm:ss
            - column:
                name: gmt_refund
                type: datetime
                constraints:
                  nullable: true
                remarks: 交易退款时间。格式为 yyyy-MM-dd HH:mm:ss.S
            - column:
                name: gmt_close
                type: datetime
                constraints:
                  nullable: true
                remarks: 交易结束时间。格式为 yyyy-MM-dd HH:mm:ss
            - column:
                name: fund_bill_list
                type: varchar(512)
                constraints:
                  nullable: true
                remarks: 支付金额信息。支付成功的各个渠道金额信息。详情可查看下文 [资金明细信息说明]
            - column:
                name: vocher_detail_list
                type: varchar(512)
                constraints:
                  nullable: true
                remarks: 优惠券信息。本交易支付时所使用的所有优惠券信息。详情可查看下表 优惠券信息说明
            - column:
                name: passback_params
                type: varchar(512)
                constraints:
                  nullable: true
                remarks: 回传参数，公共回传参数，如果请求时传递了该参数，则返回的异步通知会原样传回。本参数必须进行 UrlEncode 之后才可传入。
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
                defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-179
      author: dwy
      changes:
        - addColumn:
            tableName: alipay_pay_log
            columns:
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 内部订单号
                  afterColumn: id
  - changeSet:
      id: dwy-order-180
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `alipay_order_table` 
            CHANGE COLUMN `qrcode` `page_redirection_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '二维码' AFTER `order_num`;
  - changeSet:
      id: dwy-order-181
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `alipay_order_table` 
            MODIFY COLUMN `page_redirection_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '跳转页面数据' AFTER `order_num`;
  - changeSet:
      id: dwy-order-182
      author: dwy
      changes:
        - addColumn:
            tableName: alipay_pay_log
            columns:
              - column:
                  name: remark
                  type: json
                  constraints:
                    nullable: false
                  remarks: 备注
                  afterColumn: passback_params
  - changeSet:
      id: dwy-order-183
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `alipay_pay_log` 
            MODIFY COLUMN `notify_time` datetime NULL COMMENT '通知的发送时间' AFTER `order_num`,
            MODIFY COLUMN `notify_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '通知类型' AFTER `notify_time`,
            MODIFY COLUMN `notify_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '通知校验 ID' AFTER `notify_type`,
            MODIFY COLUMN `charset` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '编码格式。如 utf-8、gbk、gb312等。' AFTER `notify_id`,
            MODIFY COLUMN `version` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '调用的接口版本。固定为1.0' AFTER `charset`,
            MODIFY COLUMN `sign_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '签名类型。签名算法类型，目前支持RSA2和RSA，推荐使用 RSA2' AFTER `version`,
            MODIFY COLUMN `sign` varchar(344) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '签名。详情可查看 [异步返回结果的验签]' AFTER `sign_type`,
            MODIFY COLUMN `auth_app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '授权方的APPID。由于本接口暂不开放第三方应用授权，因此 auth_app_id=app_id' AFTER `sign`,
            MODIFY COLUMN `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '支付宝交易号，支付宝交易凭证号。' AFTER `auth_app_id`,
            MODIFY COLUMN `app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '支付宝应用的APPID。支付宝分配给开发者的应用 ID' AFTER `trade_no`,
            MODIFY COLUMN `out_trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '商家订单号。原支付请求的商家订单号' AFTER `app_id`;
      comment: 修改字段为可为空
  - changeSet:
      id: dwy-order-184
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `shipping_remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注' AFTER `main_carry_count`;
  - changeSet:
      id: dwy-order-184-2
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: is_default_exchange_rate
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否使用的默认汇率（0:默认,1:不是）
                  afterColumn: current_exchange_rate
                  defaultValue: 1
  - changeSet:
      id: dwy-order-185
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `is_default_exchange_rate` tinyint(1) NULL DEFAULT 0 COMMENT '是否使用的默认汇率（0:不是,1:默认）' AFTER `current_exchange_rate`;
  - changeSet:
      id: dwy-order-186
      author: dwy
      changes:
      -  renameColumn:
           columnDataType: varchar(1000)
           newColumnName: video_url
           oldColumnName: url
           remarks: 视频地址
           tableName: order_video_feed_back
  - changeSet:
      id: dwy-order-187
      author: dwy
      changes:
      - addColumn:
          tableName: order_video_feed_back
          columns:
            - column:
                name: pic_url
                type: varchar(1000)
                constraints:
                  nullable: true
                remarks: 图片地址
                afterColumn: video_url
            - column:
                name: type
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 反馈类型
                afterColumn: video_id
  - changeSet:
      id: dwy-order-188
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back` 
            MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '反馈类型（1:视频,2:视频和照片,3:照片）' AFTER `video_id`,
            MODIFY COLUMN `video_score` tinyint(1) NULL COMMENT '视频评分' AFTER `pic_url`;
      comment: 补充反馈类型备注，设置视频评分可为空
  - changeSet:
      id: dwy-order-189
      author: dwy
      changes:
        - sql: |
            UPDATE order_video_feed_back SET type = 1 WHERE type = 0
      comment: 设置反馈类型默认类型
  - changeSet:
      id: dwy-order-190
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: need_confirm_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单进入需确认的时间
                  afterColumn: un_finished_time
              - column:
                  name: last_model_submit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 最新提交模特时间
                  afterColumn: last_change_time
  - changeSet:
      id: dwy-order-191
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: operate_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 操作人
                  afterColumn: refund_status
  - changeSet:
      id: dwy-order-192
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: operate_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 操作人ID
                  afterColumn: operate_by
  - changeSet:
      id: dwy-order-193
      author: dwy
      changes:
        - sql: |
            UPDATE order_table SET is_default_exchange_rate = 0 WHERE is_default_exchange_rate = 1
      comment: 初始化is_default_exchange_rate为0
  - changeSet:
      id: dwy-order-194
      author: dwy
      changes:
        - createTable:
            tableName: order_video_match
            isNotExists: true
            remarks: 订单_视频_匹配单据表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频订单ID(FK:order_video.id)
              - column:
                  name: node
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 当前节点（1:下单支付,2:匹配模特,3:商家发货,4:完成拍摄,5:商家确认,6:订单完成,10:取消订单）
              - column:
                  name: product_pic_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 产品图变更（1：变更了，0：未变更）
                  defaultValue: 0
              - column:
                  name: goods_info_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 商品信息变更（1：变更了，0：未变更）
                  defaultValue: 0
              - column:
                  name: shoot_required_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 拍摄要求变更（1：变更了，0：未变更）
                  defaultValue: 0
              - column:
                  name: cautions_change
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 注意事项变更（1：变更了，0：未变更）
                  defaultValue: 0
              - column:
                  name: count
                  type: int(3)
                  constraints:
                    nullable: false
                  remarks: 匹配次数
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 状态（1:正常,2:暂停）
                  defaultValue: 1
              - column:
                  name: start_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 开始时间
              - column:
                  name: end_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 结束时间
              - column:
                  name: schedule_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 排单类型（1:排单,2:携带排单）
              - column:
                  name: commission_unit
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
              - column:
                  name: commission
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 模特佣金
              - column:
                  name: overstatement
                  type: varchar(5000)
                  constraints:
                    nullable: true
                  remarks: 超额说明
              - column:
                  name: carry_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 携带类型（1:主携带,2:被携带）
              - column:
                  name: main_carry_count
                  type: int(10)
                  constraints:
                    nullable: true
                  remarks: 主携带数量
              - column:
                  name: main_carry_video_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 主携带视频订单id (FK:order_video.id)
              - column:
                  name: shipping_remark
                  type: varchar(800)
                  constraints:
                    nullable: true
                  remarks: 发货备注
              - column:
                  name: shipping_pic
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 发货备注图片（FK:order_resource.id，多个用,隔开）
              - column:
                  name: submit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 提交时间
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 更新人ID
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-195
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match` 
            DROP COLUMN `node`,
            MODIFY COLUMN `product_pic_change` tinyint(1) NULL DEFAULT 0 COMMENT '产品图变更(1:变更了,0:未变更)' AFTER `video_id`,
            MODIFY COLUMN `goods_info_change` tinyint(1) NULL DEFAULT 0 COMMENT '商品信息变更(1:变更了,0:未变更)' AFTER `product_pic_change`,
            MODIFY COLUMN `shoot_required_change` tinyint(1) NULL DEFAULT 0 COMMENT '拍摄要求变更(1:变更了,0:未变更)' AFTER `goods_info_change`,
            MODIFY COLUMN `cautions_change` tinyint(1) NULL DEFAULT 0 COMMENT '注意事项变更(1:变更了,0:未变更)' AFTER `shoot_required_change`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by_id`;
  - changeSet:
      id: dwy-order-196
      author: dwy
      changes:
        - renameTable:
            oldTableName: order_video_preselect_model
            newTableName: order_video_match_preselect_model
  - changeSet:
      id: dwy-order-197
      author: dwy
      changes:
        -  setTableRemarks:
             remarks: 订单_视频_匹配单据_预选模特表
             tableName: order_video_match_preselect_model
  - changeSet:
      id: dwy-order-198
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: model_intention
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)
                  afterColumn: flag
  - changeSet:
      id: dwy-order-199
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: select_timeout
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 意向模特选择超时时间
                  afterColumn: model_intention
  - changeSet:
      id: dwy-order-200
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: process_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 意向模特超时处理时间
                  afterColumn: select_timeout
  - changeSet:
      id: dwy-order-201
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: selected_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 选定时间
                  afterColumn: process_time
  - changeSet:
      id: dwy-order-202
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `remark` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注' AFTER `status`,
            MODIFY COLUMN `model_intention` tinyint(1) NULL DEFAULT 1 COMMENT '模特意向(1:待MT确认,2:MT想要,3:MT不想要,4:未确认,5:超时未选择)' AFTER `flag`;
  - changeSet:
      id: dwy-order-203
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: match_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 匹配单ID(FK:order_video_match.id)
                  afterColumn: video_id
  - changeSet:
      id: dwy-order-204
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: select_status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 模特选择状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄）
                  afterColumn: selected_time
  - changeSet:
      id: dwy-order-205
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: select_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 选择时间
                  afterColumn: select_status
  - changeSet:
      id: dwy-order-206
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `select_time` datetime NULL DEFAULT NULL COMMENT '模特选择时间' AFTER `select_status`;
  - changeSet:
      id: dwy-order-207
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `select_status` tinyint(1) NULL DEFAULT 0 COMMENT '模特选择状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄）' AFTER `selected_time`;
  - changeSet:
      id: dwy-order-208
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video
            columnName: order_flag
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: overstatement
                  type: varchar(5000)
                  constraints:
                    nullable: true
                  remarks: 超额说明
                  afterColumn: commission
  - changeSet:
      id: dwy-order-209
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `schedule_type` tinyint(1) NULL DEFAULT NULL COMMENT '排单类型（1:排单,2:携带排单）' AFTER `video_duration`;
  - changeSet:
      id: dwy-order-210
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match` 
            MODIFY COLUMN `overstatement` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '超额说明' AFTER `commission`;
  - changeSet:
      id: dwy-order-211
      author: dwy
      changes:
        - renameColumn:
            columnDataType: tinyint(1)
            newColumnName: oust_type
            oldColumnName: flag
            remarks: 淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向）
            tableName: order_video_match_preselect_model
#  - changeSet:
#      id: dwy-order-212
#      author: dwy
#      changes:
#        - dropColumn:
#            tableName: order_video_match_preselect_model
#            columnName: video_id
#  - changeSet:
#      id: dwy-order-213
#      author: dwy
#      changes:
#        - dropTable:
#            tableName: order_video_model_select
  - changeSet:
      id: dwy-order-214
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shoot_required_original
                  type: json
                  constraints:
                    nullable: true
                  remarks: 拍摄要求原文
                  afterColumn: submit_time
  - changeSet:
      id: dwy-order-215
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shoot_required_translation
                  type: json
                  constraints:
                    nullable: true
                  remarks: 拍摄要求原文
                  afterColumn: shoot_required_original
  - changeSet:
      id: dwy-order-216
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match` 
            MODIFY COLUMN `shoot_required_translation` json NULL COMMENT '拍摄要求译文' AFTER `shoot_required_original`;
  - changeSet:
      id: dwy-order-217
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shoot_model_id
                  type: bigint
                  constraints:
                    nullable: true
                  remarks: 拍摄模特id
                  afterColumn: submit_time
  - changeSet:
      id: dwy-order-218
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_change
            columns:
              - column:
                  name: overstatement
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 超额说明
                  afterColumn: commission
  - changeSet:
      id: dwy-order-219
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `overstatement` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '超额说明' AFTER `commission`;
  - changeSet:
      id: dwy-order-220
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: model_phone_visible
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 手机号是否可见(0-不可见,1-可见)
                  afterColumn: is_care
  - changeSet:
      id: dwy-order-221
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_shipping_address
            columns:
              - column:
                  name: phone
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 手机号
                  afterColumn: detail_address
  - changeSet:
      id: dwy-order-222
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_change` 
            MODIFY COLUMN `overstatement` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '超额说明' AFTER `commission`;
            ALTER TABLE `order_video_match` 
            MODIFY COLUMN `overstatement` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '超额说明' AFTER `commission`;
            ALTER TABLE `order_video` 
            MODIFY COLUMN `overstatement` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '超额说明' AFTER `commission`;
  - changeSet:
      id: dwy-order-223
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: issue_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 模特对接客服ID
                  afterColumn: shoot_model_id
  - changeSet:
      id: dwy-order-222-1
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: reissue_cause
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 补发原因
                  afterColumn: reissue
  - changeSet:
      id: dwy-order-223-1
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_content
            columns:
              - column:
                  name: pic
                  type: varchar(700)
                  constraints:
                    nullable: true
                  remarks: 图片（FK:order_resource.id，多个用,隔开）
                  afterColumn: content
  - changeSet:
      id: dwy-order-224
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video_content
            columnName: pic
  - changeSet:
      id: dwy-order-225
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: cautions_pic
                  type: varchar(700)
                  constraints:
                    nullable: true
                  remarks: 图片（FK:order_resource.id，多个用,隔开）
                  afterColumn: model_phone_visible
  - changeSet:
      id: dwy-order-226
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `cautions_pic` varchar(700) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注意事项图片（FK:order_resource.id，多个用,隔开）' AFTER `model_phone_visible`;
  - changeSet:
      id: dwy-order-227-1733475409
      author: dwy
      changes:
        - createTable:
            tableName: order_video_task_detail
            isNotExists: true
            remarks: 订单_视频_任务单明细表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: task_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 任务单ID(FK:order_video_task.id)
              - column:
                  name: task_num
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 工单编号
              - column:
                  name: after_sale_class
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 售后分类（1：视频，2：照片）
              - column:
                  name: after_sale_video_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 售后视频类型（1：重拍视频，2：补拍视频，3：要高清视频，4：原素材，5：重新上传，6：重新剪辑）
              - column:
                  name: after_sale_pic_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 售后照片类型（1：重拍照片，2：要高清照片，3：补拍照片，4：原素材）
              - column:
                  name: work_order_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他）
              - column:
                  name: content
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 问题描述
              - column:
                  name: content_english
                  type: varchar(5000)
                  constraints:
                    nullable: true
                  remarks: 问题描述_英文
              - column:
                  name: issue_pic
                  type: varchar(700)
                  constraints:
                    nullable: true
                  remarks: 问题图片（FK:order_resource.id）
              - column:
                  name: priority
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 优先级（1:紧急,2:一般）
              - column:
                  name: submit_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 提交人姓名
              - column:
                  name: submit_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 提交人ID
              - column:
                  name: submit_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 提交时间
              - column:
                  name: assignee_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 处理人ID
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）
                  defaultValue: 1
              - column:
                  name: end_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 关闭/完成时间
              - column:
                  name: remark
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
  - changeSet:
      id: dwy-order-228-1733477521
      author: dwy
      changes:
      - createTable:
          tableName: order_video_task_detail_flow_record
          isNotExists: true
          remarks: 订单_视频_任务_流转单明细流转记录表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: task_num
                type: varchar(20)
                constraints:
                  nullable: false
                remarks: 工单编号
            - column:
                name: time
                type: datetime
                constraints:
                  nullable: false
                remarks: 记录时间
            - column:
                name: operate_by
                type: varchar(32)
                constraints:
                  nullable: false
                remarks: 操作人姓名
            - column:
                name: operate_by_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 操作人ID
            - column:
                name: operate_type
                type: tinyint(2)
                constraints:
                  nullable: true
                remarks: 操作类型
            - column:
                name: issue_pic
                type: varchar(700)
                constraints:
                  nullable: true
                remarks: 问题图片（FK:order_resource.id）
            - column:
                name: remark
                type: varchar(1000)
                constraints:
                  nullable: true
                remarks: 备注
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
  - changeSet:
      id: dwy-order-229-1733727218
      author: dwy
      changes:
      - createTable:
          tableName: order_video_task_two
          isNotExists: true
          remarks: 订单_视频_任务单表
          columns:
            - column:
                name: id
                type: bigint(20)
                autoIncrement: true
                constraints:
                  primaryKey: true
                  nullable: false
                remarks: 主键
                startWith: 3001
            - column:
                name: video_id
                type: bigint(20)
                constraints:
                  nullable: false
                remarks: 视频订单id FK:order_video.id
            - column:
                name: task_type
                type: tinyint(1)
                constraints:
                  nullable: false
                remarks: 任务单类型（1：售后单，2：工单）
            - column:
                name: create_time
                type: datetime
                constraints:
                  nullable: true
                remarks: 创建时间
  - changeSet:
      id: dwy-order-230-1733727218
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_two` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `task_type`;
            ALTER TABLE `order_video_task_detail` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `remark`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
            ALTER TABLE `order_video_task_detail_flow_record` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `remark`;
      comment:  补充创建时间更新时间默认值
  - changeSet:
      id: dwy-order-231-1733749123
      author: dwy
      changes:
        - modifyDataType:
            tableName: order_video_task_detail_flow_record
            columnName: operate_type
            newDataType: tinyint(3)
  - changeSet:
      id: dwy-order-232-1733794258
      author: dwy
      changes:
        - modifyDataType:
            tableName: order_video_task_detail_flow_record
            columnName: operate_type
            newDataType: smallint(3)
  - changeSet:
      id: dwy-order-233-1733794404
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail_flow_record` 
            MODIFY COLUMN `operate_type` smallint NOT NULL COMMENT '操作类型（详见OrderTaskDetailFlowOperateTypeEnum）' AFTER `operate_by_id`;
      comment:  operate_type设置为非空 以及设置备注
  - changeSet:
      id: dwy-order-234-1733796467
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail
            columns:
              - column:
                  name: last_reply_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 最新回复时间
                  afterColumn: assignee_id
  - changeSet:
      id: dwy-order-235-1733796467
      author: dwy
      changes:
        - createTable:
            tableName: order_video_task_work_assignee_history
            isNotExists: true
            remarks: 订单_视频_任务单_工单处理人历史记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: task_num
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 工单编号
              - column:
                  name: original_assignee_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 原处理人
              - column:
                  name: current_assignee_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 当前处理人
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
  - changeSet:
      id: dwy-order-236-1733810424
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_work_assignee_history` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `current_assignee_id`;
  - changeSet:
      id: dwy-order-237-1733885706
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail_flow_record
            columns:
              - column:
                  name: completion_mode
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
                  afterColumn: operate_type
  - changeSet:
      id: dwy-order-238-1733896169
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail_flow_record` 
            MODIFY COLUMN `completion_mode` tinyint(1) NULL DEFAULT 1 COMMENT '完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）' AFTER `operate_type`;
  - changeSet:
      id: dwy-order-239-1733896169
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_refund
            columns:
              - column:
                  name: task_detail_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 售后单/工单 ID
                  afterColumn: is_full_refund
  - changeSet:
      id: dwy-order-240-1733897979
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_refund` 
            MODIFY COLUMN `task_detail_id` varchar(100) NULL DEFAULT NULL COMMENT '售后单/工单 ID （多个逗号隔开）' AFTER `is_full_refund`;
  - changeSet:
      id: dwy-order-241-1733898333
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail_flow_record` 
            MODIFY COLUMN `completion_mode` tinyint(1) NULL DEFAULT NULL COMMENT '完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）' AFTER `operate_type`;
      comment:  completion_mode回调为默认null
  - changeSet:
      id: dwy-order-227-1733457184
      author: dwy
      changes:
        - createTable:
            tableName: order_another_pay
            isNotExists: true
            remarks: 订单_代付表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: uuid
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: uuid
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 订单号
              - column:
                  name: order_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 订单类型（0-视频订单，1-会员订单）
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 是否有效（1-有效， 0-无效）
                  defaultValue: 1
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-228-1733459834
      author: dwy
      changes:
        - createIndex:
            tableName: order_another_pay
            indexName: uk_uuid
            unique: true
            columns:
              - column:
                  name: uuid
  - changeSet:
      id: dwy-order-229-1733464463
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: link
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 代付链接
                  afterColumn: status
  - changeSet:
      id: dwy-order-230-1733465656
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建人姓名
                  afterColumn: link
  - changeSet:
      id: dwy-order-231-1733465738
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人ID
                  afterColumn: create_by
  - changeSet:
      id: dwy-order-232-1733483176
      author: dwy
      changes:
        - dropColumn:
            tableName: order_another_pay
            columnName: link
  - changeSet:
      id: dwy-order-233-1733826062
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 创建人id biz_user.id
                  afterColumn: status
  - changeSet:
      id: dwy-order-234-1733826167
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: seed_code
                  type: varchar(4)
                  constraints:
                    nullable: true
                  remarks: 种草码
                  afterColumn: status
  - changeSet:
      id: dwy-order-235-1733829583
      author: dwy
      changes:
        - update:
            tableName: order_another_pay
            columns:
              - column:
                  name: biz_user_id
                  value: 0
            where: biz_user_id IS NULL
  - changeSet:
      id: dwy-order-236-1733829970
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_another_pay` 
            MODIFY COLUMN `biz_user_id` bigint NOT NULL COMMENT '创建人id biz_user.id' AFTER `seed_code`;
      comments: biz_user_id设为非空
  - changeSet:
      id: dwy-order-237-1733831531
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: is_pay
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否已支付（1：已支付，0：未支付）
                  afterColumn: status
                  defaultValue: 0
  - changeSet:
      id: dwy-order-238-1733831587
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: pay_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 支付时间
                  afterColumn: is_pay
  - changeSet:
      id: dwy-order-239-1733831645
      author: dwy
      changes:
        - dropColumn:
            tableName: order_another_pay
            columnName: pay_time
  - changeSet:
      id: dwy-order-240-1733910768
      author: dwy
      changes:
        - createTable:
            tableName: order_video_task_detail_process_record
            isNotExists: true
            remarks: 订单_视频_任务_流转单明细处理记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: task_num
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 工单编号
              - column:
                  name: time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间
              - column:
                  name: operate_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 操作人姓名
              - column:
                  name: operate_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 操作人ID
              - column:
                  name: operate_type
                  type: smallint
                  constraints:
                    nullable: true
                  remarks: 操作类型（详见OrderTaskDetailFlowOperateTypeEnum）
              - column:
                  name: content
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 内容
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-241-1733912662
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail_process_record
            columns:
              - column:
                  name: completion_mode
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 完结方式（1：主动完结，2：补发，3：补偿，4：反馈素材给商家，5：模特反馈素材）
                  afterColumn: operate_type
  - changeSet:
      id: dwy-order-242-1733920972
      author: dwy
      changes:
        - dropTable:
            tableName: order_video_task
        - dropTable:
            tableName: order_video_task_flow_record
  - changeSet:
      id: dwy-order-243-1733921067
      author: dwy
      changes:
        - renameTable:
            oldTableName: order_video_task_two
            newTableName: order_video_task
  - changeSet:
      id: dwy-order-244-1733921297
      author: dwy
      changes:
        - setTableRemarks:
            tableName: order_video_task_detail_flow_record
            remarks: 订单_视频_任务_明细流转记录表
        - setTableRemarks:
            tableName: order_video_task_detail_process_record
            remarks: 订单_视频_任务_工单明细处理记录表
  - changeSet:
      id: dwy-order-245-1733981744
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail_process_record` 
            MODIFY COLUMN `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容' AFTER `completion_mode`;
  - changeSet:
      id: dwy-order-246-1734085802
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail_flow_record
            columns:
              - column:
                  name: appointee_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 被指派人ID
                  afterColumn: operate_type
  - changeSet:
      id: dwy-order-247-1734400869
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: commission_pays_taxes
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 佣金代缴税费
                  afterColumn: pic_price
  - changeSet:
      id: dwy-order-248-1734406808
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: pay_amount_dollar
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 需支付金额（单位：$）
                  afterColumn: pay_amount
              - column:
                  name: currency
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 币种（1:人民币,2:离岸人民币,3:美元,4:澳元,5:加币,6:英镑,7:港币,8:日元,9:新西兰元,10:新加坡元）
                  defaultValue: 1
                  afterColumn: real_pay_amount
  - changeSet:
      id: dwy-order-249-1734413097
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `currency` tinyint(1) NULL DEFAULT NULL COMMENT '币种（1:人民币,2:离岸人民币,3:美元,4:澳元,5:加币,6:英镑,7:港币,8:日元,9:新西兰元,10:新加坡元）' AFTER `real_pay_amount`;
      comments: currency改为默认null
  - changeSet:
      id: dwy-order-250-1734416563
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: amount_dollar
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 视频金额（单位：$）
                  afterColumn: amount
  - changeSet:
      id: dwy-order-251-1734417242
      author: dwy
      changes:
        - update:
            tableName: order_video
            columns:
              - column:
                  name: commission_pays_taxes
                  value: 0.00
            where: commission_pays_taxes IS NULL
      comments: 初始化commission_pays_taxes
  - changeSet:
      id: dwy-order-252-1734417743
      author: dwy
      changes:
        - addColumn:
            tableName: video_cart
            columns:
              - column:
                  name: commission_pays_taxes
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 佣金代缴税费（单位：$）
                  afterColumn: pic_price
  - changeSet:
      id: dwy-order-253-1734418139
      author: dwy
      changes:
        - update:
            tableName: video_cart
            columns:
              - column:
                  name: commission_pays_taxes
                  value: 0.00
            where: commission_pays_taxes IS NULL
      comments: 初始化commission_pays_taxes
  - changeSet:
      id: dwy-order-254-1734422895
      author: dwy
      changes:
        - update:
            tableName: order_table
            columns:
              - column:
                  name: pay_amount_dollar
                  value: 0.00
            where: pay_amount_dollar IS NULL
      comments: 初始化pay_amount_dollar
  - changeSet:
      id: dwy-order-255-1734422928
      author: dwy
      changes:
        - update:
            tableName: order_video
            columns:
              - column:
                  name: amount_dollar
                  value: 0.00
            where: amount_dollar IS NULL
      comments: 初始化amount_dollar
  - changeSet:
      id: dwy-order-256-1734423044
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `commission_pays_taxes` decimal(12, 2) NOT NULL COMMENT '佣金代缴税费（单位：$）' AFTER `pic_price`;
            ALTER TABLE `order_video` 
            MODIFY COLUMN `amount_dollar` decimal(12, 2) NOT NULL COMMENT '视频金额（单位：$）' AFTER `amount`,
            MODIFY COLUMN `commission_pays_taxes` decimal(12, 2) NOT NULL COMMENT '佣金代缴税费' AFTER `pic_price`;
            ALTER TABLE `order_table` 
            MODIFY COLUMN `pay_amount_dollar` decimal(12, 2) NOT NULL COMMENT '需支付金额（单位：$）' AFTER `pay_amount`;
  - changeSet:
      id: dwy-order-257-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_payee_account
            columns:
              - column:
                  name: company_account_type
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 收款账号类型
                  afterColumn: bank_account
  - changeSet:
      id: dwy-order-258-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_payee_account
            columns:
              - column:
                  name: company_bank_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 银行代码
                  afterColumn: company_account_type
  - changeSet:
      id: dwy-order-259-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_payee_account
            columns:
              - column:
                  name: company_bank_sub_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 分行代码
                  afterColumn: company_bank_code
  - changeSet:
      id: dwy-order-260-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_payee_account
            columns:
              - column:
                  name: company_bank_swift_code
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: SWIFT代码
                  afterColumn: company_bank_sub_code
  - changeSet:
      id: dwy-order-261-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_payee_account
            columns:
              - column:
                  name: company_bank_payee_address
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 收款人地址
                  afterColumn: company_bank_swift_code
  - changeSet:
      id: dwy-order-262-**********
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_payee_account` 
            MODIFY COLUMN `account_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '账号类型（0-默认类型, 1-银行卡账号, 2-对公账号,3-全币种账号）' AFTER `order_num`,
            MODIFY COLUMN `account_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '公司名称/账号名称' AFTER `account_type`,
            MODIFY COLUMN `bank_account` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收款账号名称/银行账号' AFTER `account_name`,
            MODIFY COLUMN `bank_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '开户行名称/银行所在地' AFTER `bank_account`;
      comments: order_payee_account修改字符长度、修改account_type备注
  - changeSet:
      id: dwy-order-263-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: oust_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 淘汰时间
                  afterColumn: oust_type
  - changeSet:
      id: dwy-order-264-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_back_modify_amount_records
            columns:
              - column:
                  name: old_pic_price
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 原图片费用（单位：$）
              - column:
                  name: new_pic_price
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 新图片费用（单位：$）
              - column:
                  name: old_commission_pays_taxes
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 原佣金代缴税费（单位：$）
              - column:
                  name: new_commission_pays_taxes
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 新佣金代缴税费（单位：$）
              - column:
                  name: old_exchange_price
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 原手续费（单位：$）
              - column:
                  name: new_exchange_price
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 新手续费（单位：$）
              - column:
                  name: old_service_price
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 原服务费（单位：$）
              - column:
                  name: new_service_price
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 新服务费（单位：$）
  - changeSet:
      id: dwy-order-265-1734501813
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_back_modify_amount_records` 
            MODIFY COLUMN `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人名称' AFTER `new_service_price`,
            MODIFY COLUMN `create_id` bigint NOT NULL COMMENT '创建人id' AFTER `create_by`,
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_id`;
      comments: 改变排序
  - changeSet:
      id: dwy-order-266-1734505790
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: payee_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 收款信息id
                  afterColumn: pay_type
  - changeSet:
      id: dwy-order-267-1734509574
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单编号 (FK:order_table.order_num)' AFTER `id`,
            MODIFY COLUMN `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '发票抬头' AFTER `merchant_id`,
            MODIFY COLUMN `duty_paragraph` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '税号' AFTER `title`,
            MODIFY COLUMN `content` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '发票内容' AFTER `duty_paragraph`,
            MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废）' AFTER `amount`;
      comments: order_invoice修改非空字段，修改status注释
  - changeSet:
      id: dwy-order-268-1734571424
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `commission_pays_taxes` decimal(12, 2) NOT NULL COMMENT '佣金代缴税费（单位：$）' AFTER `pic_price`;
      comments: commission_pays_taxes修改备注
  - changeSet:
      id: dwy-order-269-1734593024
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `pay_type` tinyint NULL DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,7:全币种,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付,17:全币种+余额）' AFTER `pay_account`;
      comments: pay_type注释添加全币种支付
  - changeSet:
      id: dwy-order-270-**********
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: model_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 模特类型(0:影响者,1:素人)
              - column:
                  name: model_platform
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
              - column:
                  name: model_cooperation
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 模特合作深度(0:一般模特,1:优质模特,2:中等模特)
              - column:
                  name: model_person_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 模特对接人ID
              - column:
                  name: model_person_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 模特对接人名称
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shoot_model_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特类型(0:影响者,1:素人)
              - column:
                  name: shoot_model_platform
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
              - column:
                  name: shoot_model_cooperation
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特合作深度(0:一般模特,1:优质模特,2:中等模特)
              - column:
                  name: shoot_model_person_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特对接人ID
              - column:
                  name: shoot_model_person_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特对接人名称
  - changeSet:
      id: dwy-order-271-1734600708
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match` 
            MODIFY COLUMN `pause_reason` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '暂停原因' AFTER `status`,
            MODIFY COLUMN `shoot_model_type` tinyint(1) NULL DEFAULT NULL COMMENT '拍摄模特类型(0:影响者,1:素人)' AFTER `shoot_model_id`,
            MODIFY COLUMN `shoot_model_platform` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '拍摄模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `shoot_model_type`,
            MODIFY COLUMN `shoot_model_cooperation` tinyint(1) NULL DEFAULT NULL COMMENT '拍摄模特合作深度(0:一般模特,1:优质模特,2:中等模特)' AFTER `shoot_model_platform`,
            MODIFY COLUMN `shoot_model_person_id` bigint NULL DEFAULT NULL COMMENT '拍摄模特对接人ID' AFTER `shoot_model_cooperation`,
            MODIFY COLUMN `shoot_model_person_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '拍摄模特对接人名称' AFTER `shoot_model_person_id`,
            MODIFY COLUMN `issue_id` bigint NULL DEFAULT NULL COMMENT '模特对接客服ID' AFTER `shoot_model_person_name`;
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `model_type` tinyint(1) NULL DEFAULT NULL COMMENT '模特类型(0:影响者,1:素人)' AFTER `model_id`,
            MODIFY COLUMN `model_platform` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `model_type`,
            MODIFY COLUMN `model_cooperation` tinyint(1) NULL DEFAULT NULL COMMENT '模特合作深度(0:一般模特,1:优质模特,2:中等模特)' AFTER `model_platform`,
            MODIFY COLUMN `model_person_id` bigint NULL DEFAULT NULL COMMENT '模特对接人ID' AFTER `model_cooperation`,
            MODIFY COLUMN `model_person_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模特对接人名称' AFTER `model_person_id`,
            MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0:未对接,1:已对接,2:已选定,3:已淘汰）' AFTER `model_person_name`;
      comments: 修改字段排序
#  - changeSet:
#      id: dwy-order-272-1734657335
#      author: dwy
#      changes:
#        - sql:
#            ALTER TABLE `order_video_match_preselect_model`
#            MODIFY COLUMN `model_type` tinyint(1) NOT NULL COMMENT '模特类型(0:影响者,1:素人)' AFTER `model_id`,
#            MODIFY COLUMN `model_platform` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)' AFTER `model_type`,
#            MODIFY COLUMN `model_cooperation` tinyint(1) NOT NULL COMMENT '模特合作深度(0:一般模特,1:优质模特,2:中等模特)' AFTER `model_platform`,
#            MODIFY COLUMN `model_person_id` bigint NOT NULL COMMENT '模特对接人ID' AFTER `model_cooperation`,
#            MODIFY COLUMN `model_person_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模特对接人名称' AFTER `model_person_id`;
#      comments: 修改模特快照字段非空
  - changeSet:
      id: dwy-order-273-1734666253
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_operate` 
            MODIFY COLUMN `event_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件内容' AFTER `event_execute_time`;
  - changeSet:
      id: dwy-order-274-1735095189
      author: dwy
      changes:
        - createTable:
            tableName: order_video_rollback_record
            isNotExists: true
            remarks: 订单_视频_回退记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频ID (FK:order_video.id)
              - column:
                  name: count
                  type: tinyint(3)
                  constraints:
                    nullable: false
                  remarks: 回退次数
              - column:
                  name: operate_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 操作人姓名
              - column:
                  name: operate_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 操作人ID
              - column:
                  name: operate_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 操作时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-275-1735095977
      author: dwy
      changes:
        - createTable:
            tableName: order_video_rollback_record_change
            isNotExists: true
            remarks: 订单_视频_回退记录_数据变更表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 回退ID (FK:order_video_rollback_record.id)
              - column:
                  name: shoot_model_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 拍摄模特ID
              - column:
                  name: issue_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 出单人ID
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）
              - column:
                  name: schedule_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 排单类型（1:排单,2:携带排单）
              - column:
                  name: commission_unit
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
              - column:
                  name: commission
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 模特佣金
              - column:
                  name: overstatement
                  type: varchar(800)
                  constraints:
                    nullable: true
                  remarks: 超额说明
              - column:
                  name: carry_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 携带类型（1:主携带,2:被携带）
              - column:
                  name: main_carry_count
                  type: int
                  constraints:
                    nullable: true
                  remarks: 主携带数量
              - column:
                  name: main_carry_video_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 主携带视频订单id (order_video_carry.main_carry_video_id)
              - column:
                  name: shipping_remark
                  type: varchar(800)
                  constraints:
                    nullable: true
                  remarks: 发货备注
              - column:
                  name: shipping_pic
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 发货备注图片（FK:order_resource.id，多个用,隔开）
              - column:
                  name: logistic_flag
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 标记物流状态（1:标记发货）
              - column:
                  name: model_phone_visible
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 手机号是否可见(0-不可见,1-可见)
              - column:
                  name: un_confirm_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单进入待确认的时间
              - column:
                  name: un_finished_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单进入待完成的时间
              - column:
                  name: need_confirm_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单进入需确认的时间
              - column:
                  name: auto_complete_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频订单自动完成时间
              - column:
                  name: last_model_submit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 最新提交模特时间
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-276-1735108162
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_shipping_address
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: video_id
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: shipping_address_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 收件地址ID (FK:order_video_model_shipping_address.id)
                  afterColumn: video_id
        - addColumn:
            tableName: order_video_feed_back
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: video_id
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: video_id
        - addColumn:
            tableName: order_video_model_change
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: video_id
  - changeSet:
      id: dwy-order-277-1735111749
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: cautions_pic
  - changeSet:
      id: dwy-order-278-1735113566
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            MODIFY COLUMN `shipping_address_id` bigint NULL DEFAULT 0 COMMENT '收件地址ID (FK:order_video_model_shipping_address.id)' AFTER `video_id`;
      comments: 临时设置默认值0
  - changeSet:
      id: dwy-order-279-1735113570
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            MODIFY COLUMN `shipping_address_id` bigint NULL DEFAULT NULL COMMENT '收件地址ID (FK:order_video_model_shipping_address.id)' AFTER `video_id`;
      comment: 又改为默认null
  - changeSet:
      id: dwy-order-280-1735113820
      author: dwy
      changes:
        - update:
            tableName: order_video_logistic
            columns:
              - column:
                  name: shipping_address_id
                  value: 0
            where: shipping_address_id IS NULL
      comments: 初始化shipping_address_id
  - changeSet:
      id: dwy-order-281-1735113909
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            MODIFY COLUMN `video_id` bigint NOT NULL COMMENT '视频订单id (FK:order_video.id)' AFTER `id`,
            MODIFY COLUMN `shipping_address_id` bigint NOT NULL DEFAULT 0 COMMENT '收件地址ID (FK:order_video_model_shipping_address.id)' AFTER `video_id`,
            MODIFY COLUMN `receipt` tinyint(1) NULL DEFAULT 0 COMMENT '是否确认收货（0-未收货, 1-已收货）' AFTER `shipping_time`;
      comments: 设置shipping_address_id、video_id为非空
  - changeSet:
      id: dwy-order-282-1735114006
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            MODIFY COLUMN `shipping_address_id` bigint NOT NULL COMMENT '收件地址ID (FK:order_video_model_shipping_address.id)' AFTER `video_id`;
      comment: 多余的shipping_address_id默认值
  - changeSet:
      id: dwy-order-283-1735115742
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_change_log
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: video_id
  - changeSet:
      id: dwy-order-284-1735116618
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID (FK:order_video_rollback_record.id)
                  afterColumn: video_id
  - changeSet:
      id: dwy-order-285-1735119277
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_rollback_record
            columns:
              - column:
                  name: cause
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 回退原因
                  afterColumn: count
  - changeSet:
      id: dwy-order-286-1735182612
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `oust_type` tinyint(1) NULL DEFAULT NULL COMMENT '淘汰类型（1:商家驳回,2:客服淘汰,3:MT不想要,4:未被选中,5:超时未选择意向,6:订单回退）' AFTER `remark`;
      comment: oust_type增加类型
  - changeSet:
      id: dwy-order-287-1735203820
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_shipping_address
            columns:
              - column:
                  name: phone_visible
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 手机号是否可见(0-不可见,1-可见)
                  afterColumn: phone
                  defaultValue: 0
              - column:
                  name: shipping_remark
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 发货备注
                  afterColumn: detail_address
                  defaultValue: ''
  - changeSet:
      id: dwy-order-288-1735204005
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_shipping_address
            columns:
              - column:
                  name: shipping_pic
                  type: varchar(200)
                  constraints:
                    nullable: true
                  remarks: 发货备注图片（FK:order_resource.id，多个用,隔开）
                  afterColumn: shipping_remark
                  defaultValue: ''
  - changeSet:
      id: dwy-order-289-1735206996
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video_rollback_record_change
            columnName: shipping_remark
        - dropColumn:
            tableName: order_video_rollback_record_change
            columnName: shipping_pic
        - dropColumn:
            tableName: order_video_rollback_record_change
            columnName: model_phone_visible
  - changeSet:
      id: dwy-order-290-1735207162
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_shipping_address` 
            MODIFY COLUMN `shipping_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发货备注' AFTER `detail_address`,
            MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发货备注图片（FK:order_resource.id，多个用,隔开）' AFTER `shipping_remark`,
            MODIFY COLUMN `phone_visible` tinyint(1) NOT NULL COMMENT '手机号是否可见(0-不可见,1-可见)' AFTER `phone`;
      comment: shipping_remark、shipping_pic、phone_visible增加非空
  - changeSet:
      id: dwy-order-291-1735215753
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_rollback_record_change
            columns:
              - column:
                  name: release_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）
                  afterColumn: last_model_submit_time
  - changeSet:
      id: dwy-order-292-1735215756
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_rollback_record_change
            columns:
              - column:
                  name: release_flag
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 订单完全释放flag（1:表示视频订单不需要再过滤24小时，可以放到模特订单公池中）
                  afterColumn: release_time
  - changeSet:
      id: dwy-order-293-1735269285
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_shipping_address` 
            MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '发货备注图片（FK:order_resource.id，多个用,隔开）' AFTER `shipping_remark`;
      comment: shipping_pic删除非空限制
  - changeSet:
      id: dwy-order-294-1735269316
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_shipping_address` 
            MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注图片（FK:order_resource.id，多个用,隔开）' AFTER `shipping_remark`;
      comment: shipping_pic删除非空限制
  - changeSet:
      id: dwy-order-295-1735293200
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: main_carry_video_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 主携带视频订单id
                  afterColumn: main_carry_count
  - changeSet:
      id: dwy-order-296-1735524528
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: carry_ignore
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 携带单忽略标记（1：表示携带该订单的主携带被回退后，会设置此值，表示该订单不计入携带数统计）
                  afterColumn: main_carry_video_id
                  defaultValue: 0
  - changeSet:
      id: dwy-order-297-1735524529
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `shipping_remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注（PS:弃用，新数据插入order_video_model_shipping_address.shipping_remark）' AFTER `carry_ignore`,
            MODIFY COLUMN `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发货备注图片（FK:order_resource.id，多个用,隔开）（PS:弃用，新数据插入order_video_model_shipping_address.shipping_pic）' AFTER `shipping_remark`,
            MODIFY COLUMN `model_phone_visible` tinyint(1) NULL DEFAULT NULL COMMENT '手机号是否可见(0-不可见,1-可见)（PS:弃用，新数据插入order_video_model_shipping_address.phone_visible）' AFTER `is_care`;
      comment: shipping_remark、shipping_pic、model_phone_visible 备注更新 新数据插入order_video_model_shipping_address
  - changeSet:
      id: dwy-order-298-1735530047
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_shipping_address
            columns:
              - column:
                  name: logistic_flag
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 标记物流状态（1:标记发货）
              - column:
                  name: logistic_flag_remark
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 标记发货备注
              - column:
                  name: logistic_flag_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 标记发货时间
  - changeSet:
      id: dwy-order-299-1735530195
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_shipping_address` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `logistic_flag_time`;
      comment: 更改排序
  - changeSet:
      id: dwy-order-300-1735541933
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_logistic
            columns:
              - column:
                  name: logistic_flag
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 标记物流状态（1:标记发货）
              - column:
                  name: logistic_flag_remark
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 标记发货备注
              - column:
                  name: logistic_flag_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 标记发货时间
  - changeSet:
      id: dwy-order-301-1735542018
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_logistic` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `logistic_flag_time`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
      comment: 更改排序
  - changeSet:
      id: dwy-order-302-1735542706
      author: dwy
      changes:
        - dropColumn:
            tableName: order_video
            columnName: logistic_flag_remark
        - dropColumn:
            tableName: order_video
            columnName: logistic_flag_time
  - changeSet:
      id: dwy-order-303-1735549372
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_operate` 
            MODIFY COLUMN `event_content` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '事件内容' AFTER `event_execute_time`;
  - changeSet:
      id: dwy-order-304-1735615096
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_model_shipping_address
            columns:
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
  - changeSet:
      id: dwy-order-305-1735615203
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_model_shipping_address` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-306-1736157560
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice
            columns:
              - column:
                  name: invoice_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 发票类型（1：增值税普通发票，2：形式发票）
                  defaultValue: 1
              - column:
                  name: title_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 抬头类型（1：企业单位）
                  defaultValue: 1
              - column:
                  name: company_name
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 公司名称
              - column:
                  name: company_address
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 公司地址
              - column:
                  name: company_phone
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 公司联系电话
              - column:
                  name: company_contact
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 公司联系人
              - column:
                  name: attachment_object_key
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 附件URI
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 发票备注
              - column:
                  name: submit_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 提交人姓名
              - column:
                  name: submit_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 提交人ID
              - column:
                  name: submit_by_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 提交时间
              - column:
                  name: is_new
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 新发票标识
              - column:
                  name: ticket_code
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 申票码
              - column:
                  name: merchant_code
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 商家编码
              - column:
                  name: source
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 来源（1：商家申请，2：红冲重开）
                  defaultValue: 1
              - column:
                  name: cautions
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 注意事项
  - changeSet:
      id: dwy-order-307-1736158569
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `ticket_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申票码' AFTER `id`,
            MODIFY COLUMN `source` tinyint(1) NULL DEFAULT 1 COMMENT '来源（1：商家申请，2：红冲重开）' AFTER `ticket_code`,
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单编号 (FK:order_table.order_num)' AFTER `source`,
            MODIFY COLUMN `merchant_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商家编码' AFTER `merchant_id`,
            MODIFY COLUMN `number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票号' AFTER `invoicing_time`,
            MODIFY COLUMN `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票抬头' AFTER `title_type`,
            MODIFY COLUMN `duty_paragraph` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税号' AFTER `title`,
            MODIFY COLUMN `content` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票内容' AFTER `attachment_object_key`,
            MODIFY COLUMN `is_new` tinyint(1) NULL DEFAULT 0 COMMENT '新发票标识（1：新发票，0：不是）' AFTER `submit_by_time`,
            MODIFY COLUMN `operator_by` bigint NULL DEFAULT NULL COMMENT '操作人' AFTER `is_new`,
            MODIFY COLUMN `operator_time` datetime NULL DEFAULT NULL COMMENT '操作时间' AFTER `operator_by`,
            MODIFY COLUMN `audit_by` bigint NULL DEFAULT 0 COMMENT '审核人' AFTER `operator_time`,
            MODIFY COLUMN `cautions` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '注意事项' AFTER `audit_by`,
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `cautions`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-307-1736158569-1
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核）' AFTER `amount`;
      comment: status补充待审核
  - changeSet:
      id: dwy-order-308-1736159246
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            CHANGE COLUMN `operator_by` `operator_by_id` bigint NULL DEFAULT NULL COMMENT '操作人ID' AFTER `is_new`,
            CHANGE COLUMN `audit_by` `audit_by_id` bigint NULL DEFAULT 0 COMMENT '审核人ID' AFTER `operator_time`;
      comment: 操作人、审核人字段修改
  - changeSet:
      id: dwy-order-309-1736159620
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票抬头' AFTER `title_type`,
            MODIFY COLUMN `duty_paragraph` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '税号' AFTER `title`;
      comment: 修改发票抬头、税号字段长度
  - changeSet:
      id: dwy-order-310-1736159753
      author: dwy
      changes:
        - createTable:
            tableName: order_invoice_video
            isNotExists: true
            remarks: 订单_发票_关联视频订单表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 发票ID (FK:order_invoice.id)
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频订单ID (FK:order_video.id)
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 订单号
              - column:
                  name: invoice_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 开票金额
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: order_invoice_operate
            isNotExists: true
            remarks: 订单_发票_操作记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 发票ID (FK:order_invoice.id)
              - column:
                  name: type
                  type: tinyint(2)
                  constraints:
                    nullable: false
                  remarks: 操作类型（详见OrderInvoiceOperateTypeEnum）
              - column:
                  name: content
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 内容
              - column:
                  name: object_key
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 图片URI
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: order_invoice_record
            isNotExists: true
            remarks: 订单_发票_开票记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 发票ID (FK:order_invoice.id)
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 类型（1：发票，2：红冲）
              - column:
                  name: invoice_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 开票金额 or 红冲金额
              - column:
                  name: number
                  type: varchar(100)
                  constraints:
                    nullable: false
                  remarks: 发票号 or 红冲票号
              - column:
                  name: invoicing_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 开票时间
              - column:
                  name: object_key
                  type: varchar(64)
                  constraints:
                    nullable: false
                  remarks: 发票URI
              - column:
                  name: remark
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: order_invoice_red
            isNotExists: true
            remarks: 订单_发票_红冲记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 发票ID (FK:order_invoice.id)
              - column:
                  name: invoice_red_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 红冲状态（1：待红冲，2：不红冲，3：已红冲）
              - column:
                  name: invoice_red_cause
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 红冲原因（1：重开发票，2：商家提现）
              - column:
                  name: invoice_red_number
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 红冲票号
              - column:
                  name: invoice_red_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 红冲金额
              - column:
                  name: object_key
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 发票文件URI
              - column:
                  name: is_reopen
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否重开（1：重开，0：不重开）
              - column:
                  name: reopen_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 重开金额
              - column:
                  name: remark
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: invoice_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 发票类型（1：增值税普通发票，2：形式发票）
                  defaultValue: 1
              - column:
                  name: title_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 抬头类型（1：企业单位）
                  defaultValue: 1
              - column:
                  name: title
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 抬头
              - column:
                  name: duty_paragraph
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 税号
              - column:
                  name: company_name
                  type: varchar(100)
                  constraints:
                    nullable: true
                  remarks: 公司名称
              - column:
                  name: company_address
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 公司地址
              - column:
                  name: company_phone
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 公司联系电话
              - column:
                  name: company_contact
                  type: varchar(150)
                  constraints:
                    nullable: true
                  remarks: 公司联系人
              - column:
                  name: attachment_object_key
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 附件URI
              - column:
                  name: content
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 发票内容
              - column:
                  name: invoice_remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 发票备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: order_invoice_red_video
            isNotExists: true
            remarks: 订单_发票_红冲_关联视频订单表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: invoice_red_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 发票红冲ID (FK:order_invoice_red.id)
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频订单ID (FK:order_video.id)
              - column:
                  name: video_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 视频编码
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-311-1736213070
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            CHANGE COLUMN `amount` `invoice_amount` decimal(12, 2) NOT NULL COMMENT '开票金额' AFTER `merchant_code`,
            CHANGE COLUMN `remark` `invoice_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票备注' AFTER `content`;
            ALTER TABLE `order_invoice_red` 
            MODIFY COLUMN `invoice_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '发票备注' AFTER `content`;
      comment: 修改字段长度及字段名称
  - changeSet:
      id: dwy-order-312-1736213070
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消）' AFTER `invoice_amount`;
      comment: 发票状态补充
  - changeSet:
      id: dwy-order-313-1736213070
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            CHANGE COLUMN `submit_by_time` `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间' AFTER `submit_by_id`;
      comment: 修改字段名称
  - changeSet:
      id: dwy-order-314-1736253852
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_video` 
            MODIFY COLUMN `video_id` bigint NULL COMMENT '视频订单ID (FK:order_video.id)' AFTER `invoice_id`;
      comment: video_id删除非空
  - changeSet:
      id: dwy-order-315-1736254294
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red
            columns:
              - column:
                  name: apply_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 申请时间
                  afterColumn: remark
  - changeSet:
      id: dwy-order-316-1736303204
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red_video
            columns:
              - column:
                  name: order_num
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 订单号
                  afterColumn: invoice_red_id
  - changeSet:
      id: dwy-order-317-1736305792
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red_video
            columns:
              - column:
                  name: refund_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 退款类型（1:补偿,2:取消订单,3:取消选配）
              - column:
                  name: withdraw_deposit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 提现时间
              - column:
                  name: withdraw_deposit_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 提现金额
  - changeSet:
      id: dwy-order-318-1736306031
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_red_video` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `withdraw_deposit_amount`;
      comment: 排序调整
  - changeSet:
      id: dwy-order-319-1736316928
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice
            columns:
              - column:
                  name: operator_by_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 操作人类型（0：运营，1：商家）
                  afterColumn: is_new
  - changeSet:
      id: dwy-order-320-1736326037
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_video
            columns:
              - column:
                  name: video_code
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 视频编码
                  afterColumn: video_id
  - changeSet:
      id: dwy-order-321
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_video` 
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号' AFTER `invoice_id`;
            ALTER TABLE `order_invoice_red_video` 
            MODIFY COLUMN `video_id` bigint NULL COMMENT '视频订单ID (FK:order_video.id)' AFTER `order_num`,
            MODIFY COLUMN `video_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '视频编码' AFTER `video_id`;
  - changeSet:
      id: dwy-order-322-1736326779
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            CHANGE COLUMN `type` `order_type` tinyint(1) NOT NULL COMMENT '订单类型（0:视频订单,1:会员订单）' AFTER `order_num`;
      comment: 修改字段名称
  - changeSet:
      id: dwy-order-323-1736393859
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '订单编号 (FK:order_table.order_num)' AFTER `source`;
      comment: order_num取消非空
  - changeSet:
      id: dwy-order-324-1736393946
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `pay_time` datetime NULL COMMENT '支付时间' AFTER `order_type`;
      comment: pay_time取消非空
  - changeSet:
      id: dwy-order-325-1736394029
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_operate` 
            MODIFY COLUMN `object_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '图片URI' AFTER `content`;
      comment: object_key取消非空
  - changeSet:
      id: dwy-order-326-1736403955
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `title_type` tinyint(1) NULL DEFAULT NULL COMMENT '抬头类型（1：企业单位）' AFTER `invoice_type`;
      comment: title_type删除默认值
  - changeSet:
      id: dwy-order-327-1736408298
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_red` 
            MODIFY COLUMN `invoice_type` tinyint(1) NULL DEFAULT NULL COMMENT '发票类型（1：增值税普通发票，2：形式发票）' AFTER `apply_time`,
            MODIFY COLUMN `title_type` tinyint(1) NULL DEFAULT NULL COMMENT '抬头类型（1：企业单位）' AFTER `invoice_type`;
  - changeSet:
      id: dwy-order-328-1736412747
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red
            columns:
              - column:
                  name: invoicing_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 开票时间
                  afterColumn: invoice_red_amount
  - changeSet:
      id: dwy-order-329-1736412830
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_red` 
            CHANGE COLUMN `invoicing_time` `invoice_red_invoicing_time` datetime NULL DEFAULT NULL COMMENT '开票时间' AFTER `invoice_red_amount`;
  - changeSet:
      id: dwy-order-330-1736479733
      author: dwy
      changes:
        - createIndex:
            tableName: order_invoice
            indexName: uk_ticket_code
            unique: true
            columns:
              - column:
                  name: ticket_code
        - dropIndex:
            indexName: uk_order_num
            tableName: order_invoice
  - changeSet:
      id: dwy-order-331-1736735819
      author: dwy
      changes:
        - createTable:
            tableName: order_invoice_order_video
            isNotExists: true
            remarks: 订单_发票_订单_关联视频表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频订单ID (FK:order_video.id)
              - column:
                  name: video_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 视频编码
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-332-1736735992
      author: dwy
      changes:
        - createTable:
            tableName: order_invoice_red_order_video
            isNotExists: true
            remarks: 订单_发票_红冲_订单_关联视频表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: invoice_red_order_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 红冲订单ID (FK:order_invoice_red_order.id)
              - column:
                  name: video_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 视频订单ID (FK:order_video.id)
              - column:
                  name: video_code
                  type: varchar(10)
                  constraints:
                    nullable: false
                  remarks: 视频编码
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-333-1736736154
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_order_video
            columns:
              - column:
                  name: invoice_order_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 发票订单ID（FK：order_invoice_order.id）
                  afterColumn: id
  - changeSet:
      id: dwy-order-334-1736739355
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red_order_video
            columns:
              - column:
                  name: refund_type
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 退款类型（1:补偿,2:取消订单,3:取消选配）
              - column:
                  name: withdraw_deposit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 提现时间
              - column:
                  name: withdraw_deposit_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 提现金额
  - changeSet:
      id: dwy-order-335-1736739414
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice_red_order_video` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `withdraw_deposit_amount`;
  - changeSet:
      id: dwy-order-336-1736747405
      author: dwy
      changes:
        - dropColumn:
            tableName: order_invoice_video
            columnName: video_id
        - dropColumn:
            tableName: order_invoice_video
            columnName: video_code
        - dropColumn:
            tableName: order_invoice_red_video
            columnName: video_id
        - dropColumn:
            tableName: order_invoice_red_video
            columnName: video_code
        - dropColumn:
            tableName: order_invoice_red_video
            columnName: refund_type
        - dropColumn:
            tableName: order_invoice_red_video
            columnName: withdraw_deposit_time
        - dropColumn:
            tableName: order_invoice_red_video
            columnName: withdraw_deposit_amount
  - changeSet:
      id: dwy-order-337-1736747480
      author: dwy
      changes:
        - renameTable:
            oldTableName: order_invoice_video
            newTableName: order_invoice_order
        - renameTable:
            oldTableName: order_invoice_red_video
            newTableName: order_invoice_red_order
  - changeSet:
      id: dwy-order-338-1736820063
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '开票状态（0:开票信息待完善,1:待开票,2:待确认,3:已投递,4:已作废,5:待审核,6:已取消,7:已重开）' AFTER `invoice_amount`;
  - changeSet:
      id: dwy-order-339-1736822586
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red
            columns:
              - column:
                  name: invoice_amount
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 开票金额
                  afterColumn: apply_time
  - changeSet:
      id: dwy-order-340-1736835535
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red
            columns:
              - column:
                  name: apply_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 申请人姓名
                  afterColumn: remark
  - changeSet:
      id: dwy-order-341-1736835567
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice_red
            columns:
              - column:
                  name: apply_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 申请人ID
                  afterColumn: apply_by
  - changeSet:
      id: dwy-order-342-1736852353
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice
            columns:
              - column:
                  name: is_apply_reopen
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 能否申请重开（1：可以，0：不行）不以此字段判断能否申请重开，只在申请后的旧发票及新发票设置0，意为此发票已申请过
                  afterColumn: cautions
                  defaultValueComputed: 0
  - changeSet:
      id: dwy-order-343-1736852380
      author: dwy
      changes:
        - createTable:
            tableName: order_invoice_reopen
            isNotExists: true
            remarks: 订单_发票_重开记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: old_invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 旧发票ID (FK:order_invoice.id)
              - column:
                  name: new_invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 新发票ID (FK:order_invoice.id)
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-344-1736852380
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `is_apply_reopen` tinyint(1) NULL DEFAULT 1 COMMENT '能否申请重开（1：可以，0：不行）不以此字段判断能否申请重开，只在申请后的旧发票及新发票设置0，意为此发票已申请过' AFTER `cautions`;
  - changeSet:
      id: dwy-order-342-1736844746
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: real_pay_amount_currency
                  type: decimal(12,2)
                  constraints:
                    nullable: true
                  remarks: 订单实付金额（对应币种实付）
                  afterColumn: real_pay_amount
  - changeSet:
      id: dwy-order-343-1736844770
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `real_pay_amount_currency` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '订单实付金额（对应币种实付）' AFTER `real_pay_amount`,
            MODIFY COLUMN `currency` tinyint(1) NULL DEFAULT NULL COMMENT '币种（详见sys_dict_type.dict_type = sys_money_type）' AFTER `real_pay_amount_currency`;
  - changeSet:
      id: dwy-order-342-1736852353
      author: dwy
      changes:
        - addColumn:
            tableName: order_invoice
            columns:
              - column:
                  name: is_apply_reopen
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 能否申请重开（1：可以，0：不行）不以此字段判断能否申请重开，只在申请后的旧发票及新发票设置0，意为此发票已申请过
                  afterColumn: cautions
                  defaultValueComputed: 0
  - changeSet:
      id: dwy-order-343-1736852380
      author: dwy
      changes:
        - createTable:
            tableName: order_invoice_reopen
            isNotExists: true
            remarks: 订单_发票_重开记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: old_invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 旧发票ID (FK:order_invoice.id)
              - column:
                  name: new_invoice_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 新发票ID (FK:order_invoice.id)
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-344-1736852380
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_invoice` 
            MODIFY COLUMN `is_apply_reopen` tinyint(1) NULL DEFAULT 1 COMMENT '能否申请重开（1：可以，0：不行）不以此字段判断能否申请重开，只在申请后的旧发票及新发票设置0，意为此发票已申请过' AFTER `cautions`;
  - changeSet:
      id: dwy-order-345-1737629952
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            MODIFY COLUMN `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客服备注' AFTER `upload_time`;
  - changeSet:
      id: dwy-order-346-1737710804
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back
            columns:
              - column:
                  name: is_new
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是否是新的（1：是，0：不是）
                  afterColumn: video_score_content
                  defaultValueComputed: 1
  - changeSet:
      id: dwy-order-347-1737710868
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back
            columns:
              - column:
                  name: modify_reason
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 修改理由
                  afterColumn: is_new
  - changeSet:
      id: dwy-order-348-1739155220
      author: dwy
      changes:
        - createIndex:
            indexName: idx_video_id_is_public_is_cart
            tableName: order_video_operate
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: is_public
              - column:
                  name: is_cart
  - changeSet:
      id: dwy-order-349-1739177834
      author: dwy
      changes:
        - createIndex:
            indexName: idx_video_id_type
            tableName: order_video_content
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: type
  - changeSet:
      id: dwy-order-350-1739177868
      author: dwy
      changes:
        - createIndex:
            indexName: idx_video_cart_id
            tableName: video_cart_content
            unique: false
            columns:
              - column:
                  name: video_cart_id
  - changeSet:
      id: dwy-order-351-1740037077142
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: carry_ignore
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 携带单忽略标记（1：表示携带该订单的主携带被回退后，且该订单状态为已完成时，会设置此值，表示该订单不计入携带数统计）
                  afterColumn: main_carry_video_id
                  defaultValue: 0
  - changeSet:
      id: dwy-order-352-1740447533637
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_rollback_record_change` 
            MODIFY COLUMN `schedule_type` tinyint(1) NULL COMMENT '排单类型（1:排单,2:携带排单）' AFTER `status`;
  - changeSet:
      id: dwy-order-351-1740463756637
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `select_status` tinyint(1) NULL DEFAULT 0 COMMENT '模特选择状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄,6:您已取消申请）' AFTER `selected_time`;
  - changeSet:
      id: dwy-order-352-1740744462113
      author: dwy
      changes:
        - createIndex:
            indexName: idx_video_id_rollback_id
            tableName: order_video_match
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: rollback_id
  - changeSet:
      id: dwy-order-353-1741052882925
      author: dwy
      changes:
        - createTable:
            tableName: order_merge
            isNotExists: true
            remarks: 订单_合并表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: merge_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 合并人姓名
              - column:
                  name: merge_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 合并人ID
              - column:
                  name: merge_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 合并时间
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 合并状态(1：正常，2：关闭)
                  defaultValue: 1
              - column:
                  name: auto_close_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 合并后自动关闭时间
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: order_merge_detail
            isNotExists: true
            remarks: 订单_合并_详情表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: merge_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 合并ID(FK:order_merge.id)
              - column:
                  name: order_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 订单ID(FK:order.id)
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-354-1741053356913
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_merge` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT 1 COMMENT '合并状态(1：正常，2：关闭)' AFTER `merge_time`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
            ALTER TABLE `order_merge_detail` 
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-355-1741066870918
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_merge_detail` 
            CHANGE COLUMN `order_id` `order_num` varchar(30) NOT NULL COMMENT '订单号(FK:order_table.order_num)' AFTER `merge_id`;
  - changeSet:
      id: dwy-order-356-1741066870918
      author: dwy
      changes:
        - addColumn:
            tableName: order_merge
            columns:
              - column:
                  name: pay_type
                  type: varchar(30)
                  constraints:
                    nullable: false
                  remarks: 支付单号
                  afterColumn: id
  - changeSet:
      id: dwy-order-357-1741066870918
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_merge` 
            CHANGE COLUMN `pay_type` `pay_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支付单号' AFTER `id`;
  - changeSet:
      id: dwy-order-358-1741233512580
      author: dwy
      changes:
        - createIndex:
            indexName: uk_pay_num
            tableName: order_merge
            unique: true
            columns:
              - column:
                  name: pay_num
  - changeSet:
      id: dwy-order-359-1741249086269
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: pay_num
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 支付单号
                  afterColumn: order_num
  - changeSet:
      id: dwy-order-360-1741249086269
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order-center`.`order_merge` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT 1 COMMENT '合并状态(1：正常，2：关闭，3：完成)' AFTER `merge_time`;
      comment: 合并状态注释修改
  - changeSet:
      id: dwy-order-361-1741250504280
      author: dwy
      changes:
        - addColumn:
            tableName: order_merge
            columns:
              - column:
                  name: complete_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 合并单完成时间
                  afterColumn: status
  - changeSet:
      id: dwy-order-362-1741313230575
      author: dwy
      changes:
        - addColumn:
            tableName: order_document_resource
            columns:
              - column:
                  name: pay_num
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 支付单号
                  afterColumn: order_num
  - changeSet:
      id: dwy-order-363-1741313352546
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_document_resource` 
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '订单号 (FK：order_table.order_num)' AFTER `id`;
  - changeSet:
      id: dwy-order-364-1741318816546
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: merge_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 合并单ID
                  afterColumn: uuid
  - changeSet:
      id: dwy-order-365-1741318965547
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_another_pay` 
            MODIFY COLUMN `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '订单号' AFTER `merge_id`,
            MODIFY COLUMN `order_type` tinyint(1) NOT NULL COMMENT '订单类型（0-视频订单，1-会员订单，4-合并单）' AFTER `order_num`;
  - changeSet:
      id: dwy-order-366-1741400522421
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_merge` 
            CHANGE COLUMN `auto_close_time` `close_merge_time` datetime NULL DEFAULT NULL COMMENT '关闭合并时间' AFTER `complete_time`;
  - changeSet:
      id: dwy-order-367-1741596833356
      author: dwy
      changes:
        - addColumn:
            tableName: order_merge
            columns:
              - column:
                  name: merge_biz_user_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 合并人biz_user_id
                  afterColumn: pay_num
              - column:
                  name: merge_business_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 合并人business_id
                  afterColumn: merge_by_id
  - changeSet:
      id: dwy-order-368-1741599336355
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_merge` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT 1 COMMENT '合并状态(1：正常，2：关闭，3：完成)' AFTER `merge_time`;
      comment: 合并状态注释修改
  - changeSet:
      id: dwy-order-369-1742527868878
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match
            columns:
              - column:
                  name: shoot_model_add_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加）
                  afterColumn: shoot_model_id
  - changeSet:
      id: dwy-order-370-1742528034867
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match` 
            MODIFY COLUMN `shoot_model_add_type` tinyint(1) NULL COMMENT '拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加）' AFTER `shoot_model_id`;
  - changeSet:
      id: dwy-order-371-1742528128875
      author: dwy
      changes:
        - sql: |
            UPDATE order_video_match 
            SET shoot_model_add_type = NULL
  - changeSet:
      id: dwy-order-367-1741743772058
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_content` 
            MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '类型（1:产品卖点（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求,4:拍摄建议,5:商品规格要求,6:特别强调）' AFTER `video_id`,
            MODIFY COLUMN `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容' AFTER `type`,
            MODIFY COLUMN `first_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '记录第一次注意事项内容' AFTER `update_time`;
  - changeSet:
      id: dwy-order-368-1741745252068
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_content_log` 
            MODIFY COLUMN `type` bigint NOT NULL COMMENT '类型（1:产品卖点（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求,4:拍摄建议,5:商品规格要求,6:特别强调）' AFTER `video_id`,
            MODIFY COLUMN `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容' AFTER `type`,
            MODIFY COLUMN `first_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '首次修改内容' AFTER `update_time`;
  - changeSet:
      id: dwy-order-369-1741748873070
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart_content` 
            MODIFY COLUMN `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容' AFTER `video_cart_id`;
  - changeSet:
      id: dwy-order-370-1741751393071
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_content` 
            MODIFY COLUMN `sort` int NULL COMMENT '排序' AFTER `content`;
      comment: 删除非空限制
  - changeSet:
      id: dwy-order-371-1741944433624
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `product_english` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`;
  - changeSet:
      id: dwy-order-372-1741944433624
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_content` 
            MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '类型（1:拍摄建议（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求,4:产品卖点,5:商品规格要求,6:特别强调）' AFTER `video_id`;
            ALTER TABLE `order_video_content_log` 
            MODIFY COLUMN `type` bigint NOT NULL COMMENT '类型（1:拍摄建议（原拍摄要求）,2:模特要求（原匹配模特注意事项）,3:剪辑要求,4:产品要求,5:商品规格要求,6:特别强调）' AFTER `video_id`;
  - changeSet:
      id: dwy-order-373-1742296053409
      author: dwy
      changes:
        - createIndex:
            indexName: idx_video_id_node
            tableName: order_video_flow_node_diagram
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: node
  - changeSet:
      id: dwy-order-374-1742451626072
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info
            columns:
              - column:
                  name: get_code
                  type: smallint(4)
                  constraints:
                    nullable: true
                  remarks: 领取编码
              - column:
                  name: get_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 领取人姓名
              - column:
                  name: get_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 领取人ID
              - column:
                  name: get_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 领取时间
              - column:
                  name: get_status
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 领取状态（0：待领取，1：已领取）
                  defaultValue: 0
              - column:
                  name: download_flag
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 标记下载（0：未标记，1：已标记）
                  defaultValue: 0
              - column:
                  name: download_flag_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 标记下载时间
              - column:
                  name: edit_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 剪辑人姓名
              - column:
                  name: edit_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 剪辑人ID
              - column:
                  name: edit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 剪辑时间
              - column:
                  name: feedback_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 反馈人姓名
              - column:
                  name: feedback_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 反馈人ID
              - column:
                  name: feedback_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 反馈时间
              - column:
                  name: feedback_status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 反馈状态（1：已反馈，2：不反馈给商家，3：订单回退）
              - column:
                  name: feedback_remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 反馈状态备注
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 状态（1：待下载，2：待剪辑，3：待反馈，4：需确认，5：已关闭）
              - column:
                  name: status_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 状态时间
              - column:
                  name: close_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 关闭人姓名
              - column:
                  name: close_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 关闭人ID
              - column:
                  name: video_score
                  type: float(2,1)
                  constraints:
                    nullable: true
                  remarks: 视频评分
              - column:
                  name: video_score_content
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 视频评分内容
  - changeSet:
      id: dwy-order-375-1742451626073
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `video_score_content`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-376-1742453691075
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back
            columns:
              - column:
                  name: material_info_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 模特反馈素材ID（FK：order_video_feed_back_material_info.id）
  - changeSet:
      id: dwy-order-377-1742453866134
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back` 
            MODIFY COLUMN `material_info_id` bigint NULL DEFAULT NULL COMMENT '模特反馈素材ID（FK：order_video_feed_back_material_info.id）' AFTER `video_id`;
  - changeSet:
      id: dwy-order-378-1742453903073
      author: dwy
      changes:
        - createTable:
            tableName: order_video_feed_back_material_info_task
            isNotExists: true
            remarks: 订单_视频_订单反馈表(模特)_详细信息_关联任务表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: material_info_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特反馈素材ID（FK：order_video_feed_back_material_info.id）
              - column:
                  name: task_detail_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 任务单明细ID（FK：order_video_task_detail.id）
  - changeSet:
      id: dwy-order-379-1742454228073
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_content
            columns:
              - column:
                  name: submit_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 提交人姓名
              - column:
                  name: submit_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 提交人ID
              - column:
                  name: submit_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 提交时间
  - changeSet:
      id: dwy-order-380-1742454347073
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_content` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `submit_time`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-381-1742454516072
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: status_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 状态时间
              - column:
                  name: close_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 关闭人姓名
              - column:
                  name: close_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 关闭人ID
              - column:
                  name: asin
                  type: varchar(20)
                  constraints:
                    nullable: true
                  remarks: asin
              - column:
                  name: upload_account
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 上传账号
              - column:
                  name: operate_remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 操作备注
  - changeSet:
      id: dwy-order-382-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            CHANGE COLUMN `is_upload` `status` tinyint(1) NULL DEFAULT 1 COMMENT '上传状态 （0:已上传,1:未上传，2：待确认上传，3：取消上传）' AFTER `need_upload_link`,
            MODIFY COLUMN `status_time` datetime NULL DEFAULT NULL COMMENT '状态时间' AFTER `status`,
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `operate_remark`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
  - changeSet:
      id: dwy-order-383-1742454966077
      author: dwy
      changes:
        - createTable:
            tableName: order_video_upload_link_record
            isNotExists: true
            remarks: 订单_视频_上传平台素材_记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: upload_link_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 上传素材ID（FK：order_video_upload_link.id）
              - column:
                  name: count
                  type: smallint(2)
                  constraints:
                    nullable: false
                  remarks: 上传次数
                  defaultValue: 1
              - column:
                  name: need_upload_link
                  type: varchar(1000)
                  constraints:
                    nullable: false
                  remarks: 需要上传的链接
              - column:
                  name: video_title
                  type: varchar(60)
                  constraints:
                    nullable: false
                  remarks: 视频标题
              - column:
                  name: upload_account
                  type: varchar(50)
                  constraints:
                    nullable: true
                  remarks: 上传账号
              - column:
                  name: video_cover
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 视频封面图URI
              - column:
                  name: remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 备注
              - column:
                  name: upload_link
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 上传的链接
              - column:
                  name: status
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传）
              - column:
                  name: upload_user_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 上传用户id
              - column:
                  name: upload_user_name
                  type: varchar(30)
                  constraints:
                    nullable: true
                  remarks: 上传用户名称
              - column:
                  name: upload_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 上传时间
              - column:
                  name: operate_remark
                  type: varchar(300)
                  constraints:
                    nullable: true
                  remarks: 操作备注
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
  - changeSet:
      id: dwy-order-384-1742540622747
      author: dwy
      changes:
        - renameTable:
            oldTableName: order_video_feed_back_material_info_task
            newTableName: order_video_feed_back_material_info_task_detail
  - changeSet:
      id: dwy-order-385-1742540622747
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info_task_detail` 
            CHANGE COLUMN `material_info_id` `video_id` bigint NOT NULL COMMENT '视频订单ID（FK：order_video.id）' AFTER `id`,
            ADD COLUMN `rollback_id` bigint NULL COMMENT '回退ID' AFTER `video_id`;
  - changeSet:
      id: dwy-order-386-1742545311
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `video_score` float(3, 1) NULL DEFAULT NULL COMMENT '视频评分' AFTER `close_by_id`;
      comment: 长度修改
  - changeSet:
      id: dwy-order-387-1742546815520
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `feedback_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈状态备注' AFTER `feedback_status`;
      comment: 长度修改
  - changeSet:
      id: dwy-order-388-1742794340246
      author: dwy
      changes:
        - sql: |
            UPDATE order_video_feed_back_material_info 
            SET `status` = 1 
            WHERE
            	`status` IS NULL
  - changeSet:
      id: dwy-order-389-1742794452244
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（1：待下载，2：待剪辑，3：待反馈，4：需确认，5：已关闭）' AFTER `feedback_remark`;
  - changeSet:
      id: dwy-order-390-1742800446243
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link_record` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT NULL COMMENT '上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，4：上传失败）' AFTER `upload_link`;
  - changeSet:
      id: dwy-order-391-1742800446243
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail` 
            MODIFY COLUMN `work_order_type` tinyint(1) NULL DEFAULT NULL COMMENT '工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）' AFTER `after_sale_pic_type`;
      comment: work_order_type上传异常类型添加
  - changeSet:
      id: dwy-order-392-1742870951641
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '链接' AFTER `material_id`,
            MODIFY COLUMN `upload_time` datetime NULL COMMENT '上传时间' AFTER `note`,
            MODIFY COLUMN `object` tinyint(1) NULL COMMENT '上传对象（2:运营,3:模特）' AFTER `upload_time`,
            MODIFY COLUMN `user_id` bigint NULL COMMENT '上传用户id' AFTER `object`;
      comment: 取消这些字段非空限制
  - changeSet:
      id: dwy-order-393-1742882328634
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail_flow_record
            columns:
              - column:
                  name: operate_by_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 操作人类型（1：处理人，2：剪辑人）
                  defaultValue: 1
                  afterColumn: operate_by_id
  - changeSet:
      id: dwy-order-394-1742884382638
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT 1 COMMENT '上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）' AFTER `need_upload_link`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-395-1742886536635
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info
            columns:
              - column:
                  name: close_by_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 关闭人类型（0：商家，1：运营）
                  defaultValue: 1
                  afterColumn: close_by_id
  - changeSet:
      id: dwy-order-396-1742886536635
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `close_by_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '关闭人类型（0：运营，1：商家）' AFTER `close_by_id`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-397-1742891389636
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `feedback_status` tinyint(1) NULL DEFAULT NULL COMMENT '反馈状态（1：已反馈，2：不反馈给商家，3：订单回退，4：联动关闭）' AFTER `feedback_time`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-398-1742892797636
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail
            columns:
              - column:
                  name: clip_record
                  type: varchar(1000)
                  constraints:
                    nullable: true
                  remarks: 补充剪辑要求
                  afterColumn: content_english
  - changeSet:
      id: dwy-order-399-1742894000640
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info_task_detail
            columns:
              - column:
                  name: material_info_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 模特反馈素材ID（FK：order_video_feed_back_material_info.id）
                  afterColumn: id
  - changeSet:
      id: dwy-order-400-1742894943636
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info_task_detail
            columns:
              - column:
                  name: feed_back_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 反馈给商家素材ID（FK：order_video_feed_back.id）
                  afterColumn: material_info_id
  - changeSet:
      id: dwy-order-401-1742895034635
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info_task_detail` 
            MODIFY COLUMN `material_info_id` bigint NULL COMMENT '模特反馈素材ID（FK：order_video_feed_back_material_info.id）' AFTER `id`;
      comment: 删除material_info_id非空限制
  - changeSet:
      id: dwy-order-402-1742951559716
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info
            columns:
              - column:
                  name: video_score_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 视频评分人
              - column:
                  name: video_score_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 视频评分人ID
              - column:
                  name: video_score_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频评分时间
        - addColumn:
            tableName: order_video_feed_back
            columns:
              - column:
                  name: video_score_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 视频评分人
              - column:
                  name: video_score_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 视频评分人ID
              - column:
                  name: video_score_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频评分时间
  - changeSet:
      id: dwy-order-403-1742952216715
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back_material_info` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `video_score_time`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
            ALTER TABLE `order_video_feed_back` 
            MODIFY COLUMN `video_score_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '视频评分人' AFTER `video_score_content`,
            MODIFY COLUMN `video_score_by_id` bigint NULL DEFAULT NULL COMMENT '视频评分人ID' AFTER `video_score_by`,
            MODIFY COLUMN `video_score_time` datetime NULL DEFAULT NULL COMMENT '视频评分时间' AFTER `video_score_by_id`;
  - changeSet:
      id: dwy-order-404-1742958053719
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail
            columns:
              - column:
                  name: rollback_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 回退ID
                  afterColumn: task_id
  - changeSet:
      id: dwy-order-405-1742959457717
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back` 
            MODIFY COLUMN `video_score` float(3, 1) NULL DEFAULT NULL COMMENT '视频评分' AFTER `pic_url`;
      comment: 数据类型修改
  - changeSet:
      id: dwy-order-406-1742977064230
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            MODIFY COLUMN `object` tinyint(1) NULL COMMENT '提交信息对象（1:商家,2:运营）' AFTER `video_id`,
            MODIFY COLUMN `user_id` bigint NULL COMMENT '提交信息用户id' AFTER `object`,
            MODIFY COLUMN `time` datetime NULL COMMENT '提交信息时间' AFTER `user_id`,
            MODIFY COLUMN `need_upload_link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '需要上传的链接' AFTER `time`;
      comment: 取消非空限制
  - changeSet:
      id: dwy-order-407-1742977222225
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link` 
            MODIFY COLUMN `video_title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '视频标题' AFTER `status_time`;
      comment: 取消非空限制
  - changeSet:
      id: dwy-order-408-1742979863235
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail_flow_record` 
            MODIFY COLUMN `operate_by_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '操作人类型（1：处理人，2：剪辑人，3：系统）' AFTER `operate_by_id`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-409-1742980310226
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_task_detail_process_record
            columns:
              - column:
                  name: operate_by_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 操作人类型（1：处理人，2：剪辑人，3：系统）
                  defaultValue: 1
                  afterColumn: operate_by_id
  - changeSet:
      id: dwy-order-410-1742980702229
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_task_detail_flow_record` 
            MODIFY COLUMN `operate_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '操作人姓名' AFTER `time`,
            MODIFY COLUMN `operate_by_id` bigint NULL COMMENT '操作人ID' AFTER `operate_by`;
            ALTER TABLE `order_video_task_detail_process_record` 
            MODIFY COLUMN `operate_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '操作人姓名' AFTER `time`,
            MODIFY COLUMN `operate_by_id` bigint NULL COMMENT '操作人ID' AFTER `operate_by`;
  - changeSet:
      id: dwy-order-411-1743040137469
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info
            columns:
              - column:
                  name: task_detail_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 任务详情ID
                  afterColumn: material_id
  - changeSet:
      id: dwy-order-374-1742296053409
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `product_chinese` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品中文名' AFTER `video_code`,
            MODIFY COLUMN `product_english` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`;
      comment: 修改产品中文名、英文名长度
  - changeSet:
      id: dwy-order-375-1742384474943
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `video_cart` 
            MODIFY COLUMN `product_chinese` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品中文名' AFTER `product_pic`,
            MODIFY COLUMN `product_english` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名' AFTER `product_chinese`;
  - changeSet:
      id: dwy-order-376-1743472668532
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info
            columns:
              - column:
                  name: enter_the_download_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 进入待下载时间
                  afterColumn: status_time
  - changeSet:
      id: dwy-order-377-1743472982527
      author: dwy
      changes:
        - sql: |
            UPDATE order_video_feed_back_material_info 
            SET enter_the_download_time = upload_time
  - changeSet:
      id: dwy-order-378-1743495986424
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back
            columns:
              - column:
                  name: feedback_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 反馈人姓名
              - column:
                  name: feedback_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 反馈人ID
              - column:
                  name: feedback_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 反馈时间
  - changeSet:
      id: dwy-order-379-1743496136429
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_feed_back` 
            MODIFY COLUMN `feedback_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '反馈人姓名' AFTER `over_time`,
            MODIFY COLUMN `feedback_by_id` bigint NULL DEFAULT NULL COMMENT '反馈人ID' AFTER `feedback_by`,
            MODIFY COLUMN `feedback_time` datetime NULL DEFAULT NULL COMMENT '反馈时间' AFTER `feedback_by_id`;
      comment: 排序修改
  - changeSet:
      id: dwy-order-380-1743500700426
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info
            columns:
              - column:
                  name: close_reason
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 关闭原因（2：不反馈给商家，3：订单回退，4：联动关闭）
                  afterColumn: close_by_id
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: close_reason
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 关闭原因（1：取消上传）
                  afterColumn: close_by_id
  - changeSet:
      id: dwy-order-381-1743501961438
      author: dwy
      changes:
        - sql:
            ALTER TABLE `order_video_feed_back_material_info`
            MODIFY COLUMN `feedback_status` tinyint(1) NULL DEFAULT NULL COMMENT '反馈状态（1：已反馈，2：不反馈给商家，4：联动关闭）' AFTER `feedback_time`;
  - changeSet:
      id: dwy-order-382-1743580112894
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link_record` 
            MODIFY COLUMN `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注' AFTER `video_cover`;
      comment: 修改字符长度
  - changeSet:
      id: dwy-order-383-1743647780735
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_upload_link_record` 
            MODIFY COLUMN `status` tinyint(1) NULL DEFAULT 1 COMMENT '上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，4：上传失败）' AFTER `upload_link`;
      comment: 设置默认值
  - changeSet:
      id: dwy-order-384-1744018208208
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_feed_back_material_info_task_detail
            columns:
              - column:
                  name: submit_by
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 提交人姓名
              - column:
                  name: submit_by_id
                  type: bigint(20)
                  constraints:
                    nullable: true
                  remarks: 提交人ID
  - changeSet:
      id: dwy-order-385-1744188596590
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: auto_complete_start_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 视频订单自动完成起始时间
                  afterColumn: auto_complete_time
  - changeSet:
      id: dwy-order-386-1744362350110
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: order_user_name
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 下单用户名称
                  afterColumn: order_user_id
              - column:
                  name: pay_user_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 支付用户名称
                  afterColumn: pay_user_id
  - changeSet:
      id: dwy-order-387-1744364365116
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: order_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 下单用户微信名称
                  afterColumn: order_user_name
              - column:
                  name: pay_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 支付用户微信名称
                  afterColumn: pay_user_name
  - changeSet:
      id: dwy-order-389-1744368491460
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: create_order_operation_user_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户名称（下单运营）
              - column:
                  name: create_order_operation_user_nick_name
                  type: varchar(32)
                  constraints:
                    nullable: true
                  remarks: 创建订单用户微信名称（下单运营）
  - changeSet:
      id: dwy-order-390-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `create_order_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建订单用户名称（订单运营）' AFTER `create_order_user_account`,
            MODIFY COLUMN `create_order_user_nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建订单用户微信名称（订单运营）' AFTER `create_order_user_name`,
            MODIFY COLUMN `create_order_operation_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建订单用户名称（下单运营）' AFTER `create_order_user_nick_name`,
            MODIFY COLUMN `create_order_operation_user_nick_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建订单用户微信名称（下单运营）' AFTER `create_order_operation_user_name`;
  - changeSet:
      id: dwy-order-391-*************
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: order_user_account
                  type: varchar(20)
                  constraints:
                    nullable: false
                  remarks: 下单用户账号
              - column:
                  name: pay_user_account
                  type: varchar(20)
                  constraints:
                    nullable: true
                  remarks: 支付用户账号
  - changeSet:
      id: dwy-order-392-*************
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `order_user_account` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '下单用户账号' AFTER `order_user_id`,
            MODIFY COLUMN `pay_user_account` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支付用户账号' AFTER `pay_user_id`;
      comment: 修改排序
  - changeSet:
      id: dwy-order-393-*************
      author: dwy
      changes:
        - addColumn:
            tableName: order_merge
            columns:
              - column:
                  name: merge_nick_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 合并人微信名
                  afterColumn: merge_biz_user_id
  - changeSet:
      id: dwy-order-394-*************
      author: dwy
      changes:
        - addColumn:
            tableName: order_another_pay
            columns:
              - column:
                  name: create_nick_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 创建人微信名
                  afterColumn: biz_user_id
  - changeSet:
      id: dwy-order-395-1744697961420
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_another_pay` 
            MODIFY COLUMN `create_nick_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '创建人微信名' AFTER `biz_user_id`;
  - changeSet:
      id: dwy-order-390-1744353760926
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_upload_link
            columns:
              - column:
                  name: video_title_first
                  type: varchar(60)
                  constraints:
                    nullable: true
                  remarks: 视频标题首次信息
                  afterColumn: video_title
              - column:
                  name: video_cover_first
                  type: varchar(64)
                  constraints:
                    nullable: true
                  remarks: 视频封面图URI首次信息
                  afterColumn: video_cover
  - changeSet:
      id: dwy-order-396-1744959025423
      author: dwy
      changes:
        - addColumn:
            tableName: order_promotion_detail
            columns:
              - column:
                  name: discount_amount_dollar
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 优惠金额（单位：$）
                  afterColumn: amount
  - changeSet:
      id: dwy-order-397-1744959201422
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_promotion_detail` 
            CHANGE COLUMN `amount` `discount_amount` decimal(12, 2) NOT NULL COMMENT '优惠金额（单位：￥）' AFTER `video_code`;
      comment: 字段名称修改
  - changeSet:
      id: dwy-order-398-1744960860419
      author: dwy
      changes:
        - sql: |
            INSERT INTO `promotion_activity` (`id`, `type`, `activity_name`, `start_time`, `end_time`, `activity_status`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`)
            VALUES (2, 2, '会员订单临期续费半价优惠', '2025-04-18 00:00:00', '3000-01-01 00:00:00', '1', '1', 'admin', NOW(), '1', 'admin', NOW());
      comment: 初始化 会员订单临期续费半价优惠 活动
  - changeSet:
      id: dwy-order-399-1745204838247
      author: dwy
      changes:
        - sql: |
            INSERT INTO `promotion_activity` (`id`, `type`, `activity_name`, `start_time`, `end_time`, `activity_status`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`)
            VALUES (3, 3, '种草码优惠', '2025-04-18 00:00:00', '3000-01-01 00:00:00', '1', '1', 'admin', NOW(), '1', 'admin', NOW());
      comment: 初始化 种草码优惠 活动
  - changeSet:
      id: dwy-order-400-1745205537248
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video` 
            MODIFY COLUMN `pay_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '需支付金额（单位：￥）' AFTER `amount_dollar`,
            MODIFY COLUMN `pay_amount_dollar` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '需支付金额（单位：$）' AFTER `pay_amount`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-401-1745403506195
      author: dwy
      changes:
        - dropColumn:
            tableName: order_promotion_detail
            columnName: video_code
  - changeSet:
      id: dwy-order-402-1745746656672
      author: dwy
      changes:
        - addColumn:
            tableName: order_table
            columns:
              - column:
                  name: pay_type_detail
                  type: tinyint(2)
                  constraints:
                    nullable: true
                  remarks: 支付方式明细（1：其他平台/银行支付，2：万里汇）
                  afterColumn: pay_type
  - changeSet:
      id: dwy-order-403-1745747312672
      author: dwy
      changes:
        - addColumn:
            tableName: order_pay_log
            columns:
              - column:
                  name: pay_type_detail
                  type: tinyint(2)
                  constraints:
                    nullable: true
                  remarks: 支付方式明细（1：其他平台/银行支付，2：万里汇）
                  afterColumn: pay_type
  - changeSet:
      id: dwy-order-404-1745748379676
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `pay_type_detail` tinyint NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
            ALTER TABLE `order_pay_log` 
            MODIFY COLUMN `pay_type_detail` tinyint NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-405-1745812159677
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `pay_type_detail` tinyint(3) NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
      comment: 修改字段长度
  - changeSet:
      id: dwy-order-406-1745812235087
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_pay_log` 
            MODIFY COLUMN `pay_type_detail` tinyint(3) NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
      comment: 修改字段长度
  - changeSet:
      id: dwy-order-407-1745812428807
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_table` 
            MODIFY COLUMN `pay_type_detail` SMALLINT NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
            ALTER TABLE `order_pay_log` 
            MODIFY COLUMN `pay_type_detail` SMALLINT NULL DEFAULT NULL COMMENT '支付方式明细（701：其他平台/银行支付，702：万里汇）' AFTER `pay_type`;
      comment: 修改数据类型
  - changeSet:
      id: dwy-order-409-1745892583452
      author: dwy
      changes:
        - sql: |
            UPDATE order_table 
            SET pay_type_detail = 701 
            WHERE
            	pay_type_detail IS NULL AND ( pay_type = 7 OR pay_type = 17 );
            UPDATE order_pay_log 
            SET pay_type_detail = 701
            WHERE
            	pay_type_detail IS NULL AND ( pay_type = 7 OR pay_type = 17 );
      comment: 初始化pay_type_detail数据
  - changeSet:
      id: dwy-order-410-1747358408604
      author: dwy
      changes:
        - createTable:
            tableName: customer_service_data_statistics_day
            isNotExists: true
            remarks: 客服数据统计_每日记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: write_time_begin
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-开始
              - column:
                  name: write_time_end
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-结束
              - column:
                  name: chinese_customer_service_added_complete_order_count_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 中文部客服新增/完成订单数量JSON
              - column:
                  name: chinese_customer_service_added_complete_task_count_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 中文部客服新增/完成任务单数量JSON
              - column:
                  name: english_customer_service_added_complete_task_count_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 英文部客服新增/完成订单数量JSON
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
        - createTable:
            tableName: customer_service_data_statistics_month
            isNotExists: true
            remarks: 客服数据统计_每月记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: write_time_begin
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-开始
              - column:
                  name: write_time_end
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-结束
              - column:
                  name: english_customer_service_added_oust_model_count_json
                  type: json
                  constraints:
                    nullable: true
                  remarks: 英文部客服新增/淘汰模特数量JSON
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-411-1747808279027
      author: dwy
      changes:
        - createTable:
            tableName: promotion_activity_detail
            isNotExists: true
            remarks: 活动信息_详情表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: activity_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 活动ID
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 优惠扣减类型（1：直减，2：折扣）
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 优惠数值
              - column:
                  name: currency
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 币种
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        - createTable:
            tableName: promotion_activity_amendment_record
            isNotExists: true
            remarks: 活动信息_修改记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: activity_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 活动ID
              - column:
                  name: type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 优惠扣减类型（1：直减，2：折扣）
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 优惠数值
              - column:
                  name: currency
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 币种
              - column:
                  name: start_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 开始时间
              - column:
                  name: end_time
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 结束时间
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: update_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 更新时间
                  defaultValueComputed: CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-412-1747811112029
      author: dwy
      changes:
        - sql: |
            INSERT INTO `promotion_activity` (`id`, `type`, `activity_name`, `start_time`, `end_time`, `activity_status`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`)
            VALUES (4, 4, '每月首单立减', NOW(), '2025-12-31 23:59:59', '1', '1', 'admin', NOW(), '1', 'admin', NOW());
            INSERT INTO `promotion_activity_detail` (`id`, `activity_id`, `type`, `amount`, `currency`, `create_time`, `update_time`)
            VALUES (1, 4, 1, 19.9, 'USD', NOW(), NOW());
  - changeSet:
      id: dwy-order-413-1747814112031
      author: dwy
      changes:
        - addColumn:
            tableName: promotion_activity_amendment_record
            columns:
              - column:
                  name: create_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 创建人姓名
              - column:
                  name: create_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 创建人ID
              - column:
                  name: update_by
                  type: varchar(32)
                  constraints:
                    nullable: false
                  remarks: 更新人姓名
              - column:
                  name: update_by_id
                  type: bigint(20)
                  constraints:
                    nullable: false
                  remarks: 更新人ID
  - changeSet:
      id: dwy-order-414-1747814460028
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `promotion_activity_amendment_record` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_by_id`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by_id`;
      comment: 排序修改
  - changeSet:
      id: dwy-order-415-1747816835033
      author: dwy
      changes:
        - sql: |
            INSERT INTO `promotion_activity_detail` (`id`, `activity_id`, `type`, `amount`, `currency`, `create_time`, `update_time`)
            VALUES (2, 1, 1, 20, 'CNY', NOW(), NOW());
            INSERT INTO `promotion_activity_detail` (`id`, `activity_id`, `type`, `amount`, `currency`, `create_time`, `update_time`)
            VALUES (3, 2, 2, 50, null, NOW(), NOW());
      comment: 初始化
  - changeSet:
      id: dwy-order-416-1747907378428
      author: dwy
      changes:
        - addColumn:
            tableName: order_promotion_detail
            columns:
              - column:
                  name: discount_type
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  remarks: 优惠扣减类型（1：直减，2：折扣）
              - column:
                  name: amount
                  type: decimal(12,2)
                  constraints:
                    nullable: false
                  remarks: 优惠数值
              - column:
                  name: currency
                  type: varchar(10)
                  constraints:
                    nullable: true
                  remarks: 币种
  - changeSet:
      id: dwy-order-417-1747908614428
      author: dwy
      changes:
        - sql: |
            UPDATE order_promotion_detail SET discount_type = 1, amount = 20, currency = 'CNY'  WHERE activity_id = 1;
            UPDATE order_promotion_detail SET discount_type = 2, amount = 50 WHERE activity_id = 2;
            UPDATE order_promotion_detail opd JOIN order_table ot ON opd.order_num = ot.order_num SET opd.amount = ot.settle_rage, opd.discount_type = 2 WHERE opd.activity_id = 3;
      comment: 初始化数据
  - changeSet:
      id: dwy-order-418-1747908710429
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_promotion_detail` 
            MODIFY COLUMN `discount_type` tinyint(1) NOT NULL COMMENT '优惠扣减类型（1：直减，2：折扣）' AFTER `discount_amount_dollar`,
            MODIFY COLUMN `amount` decimal(12, 2) NOT NULL COMMENT '优惠数值' AFTER `discount_type`,
            MODIFY COLUMN `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '币种' AFTER `amount`,
            MODIFY COLUMN `create_user_id` bigint NULL DEFAULT NULL COMMENT '创建人id' AFTER `currency`;
      comment: 修改排序
  - changeSet:
      id: dwy-order-419-1748918438729
      author: dwy
      changes:
        - createIndex:
            indexName: idx_material_task_time
            tableName: order_video_feed_back_material_info
            unique: false
            columns:
              - column:
                  name: material_id
              - column:
                  name: task_detail_id
              - column:
                  name: status_time
  - changeSet:
      id: dwy-order-420-1748918668722
      author: dwy
      changes:
        - createIndex:
            indexName: idx_vid_rid_source_time
            tableName: order_video_model_change
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: rollback_id
              - column:
                  name: selected_time
                  descending: true
  - changeSet:
      id: dwy-order-419-1749004233070
      author: dwy
      changes:
        - createTable:
            tableName: order_video_data_statistics_day
            isNotExists: true
            remarks: 视频订单数据统计_每日记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: write_time_begin
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-开始
              - column:
                  name: write_time_end
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-结束
              - column:
                  name: added_order_video_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 新增视频订单数量
                  defaultValue: 0
              - column:
                  name: added_order_video_task_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 新增视频任务单数量
                  defaultValue: 0
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-420-1749202260005
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `customer_service_data_statistics_day` 
            CHANGE COLUMN `english_customer_service_added_complete_task_count_json` `english_customer_service_added_complete_order_count_json` json NULL COMMENT '英文部客服新增/完成订单数量JSON' AFTER `chinese_customer_service_added_complete_task_count_json`;
      comment: 列名修改
  - changeSet:
      id: dwy-order-419-1749004233070
      author: dwy
      changes:
        - createTable:
            tableName: order_video_data_statistics_day
            isNotExists: true
            remarks: 视频订单数据统计_每日记录表
            columns:
              - column:
                  name: id
                  type: bigint(20)
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
                  startWith: 3001
              - column:
                  name: write_time_begin
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-开始
              - column:
                  name: write_time_end
                  type: datetime
                  constraints:
                    nullable: false
                  remarks: 记录时间-结束
              - column:
                  name: added_order_video_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 新增视频订单数量
                  defaultValue: 0
              - column:
                  name: added_order_video_task_count
                  type: int
                  constraints:
                    nullable: false
                  remarks: 新增视频任务单数量
                  defaultValue: 0
              - column:
                  name: create_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 创建时间
                  defaultValueComputed: CURRENT_TIMESTAMP
  - changeSet:
      id: dwy-order-421-1749795838081
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: cautions
                  type: varchar(500)
                  constraints:
                    nullable: true
                  remarks: 拍摄模特注意事项
                  afterColumn: select_time
  - changeSet:
      id: dwy-order-422-1749797788074
      author: dwy
      changes:
        - sql:
            ALTER TABLE `order_video_match_preselect_model`
            CHANGE COLUMN `cautions` `shoot_attention` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '拍摄模特注意事项' AFTER `select_time`;
      comment: 字段更名
  - changeSet:
      id: dwy-order-423-1750389425875
      author: dwy
      changes:
        - createIndex:
            tableName: order_video_flow
            indexName: idx_vid_ts
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: target_status
  - changeSet:
      id: dwy-order-429-1750664789677
      author: dwy
      changes:
        - addColumn:
            tableName: order_video
            columns:
              - column:
                  name: is_gund
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 是通品（1：是，0：不是）
                  afterColumn: release_flag
  - changeSet:
      id: dwy-order-423-1750143891294
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `add_type` tinyint(1) NOT NULL COMMENT '添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）' AFTER `video_id`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-424-1750144093280
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: distribution_result
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发，5：订单暂停匹配）
              - column:
                  name: distribution_result_time
                  type: datetime
                  constraints:
                    nullable: true
                  remarks: 分发结果时间
  - changeSet:
      id: dwy-order-425-1750144276291
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `distribution_result_time`,
            MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`;
      comment: 排序修改
  - changeSet:
      id: dwy-order-426-1750212343875
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: distribution_result_cause
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 分发结果原因（1：客服取消，2：订单回退，3：订单暂停匹配，4：模特行程中，5：模特暂停合作，6：模特取消合作，7：未确认，8：交易关闭）
                  afterColumn: distribution_result
  - changeSet:
      id: dwy-order-428-1750322291873
      author: dwy
      changes:
        - addColumn:
            tableName: order_video_match_preselect_model
            columns:
              - column:
                  name: need_remind_shoot_attention
                  type: tinyint(1)
                  constraints:
                    nullable: true
                  remarks: 需要提醒拍摄模特注意事项
                  afterColumn: shoot_attention
  - changeSet:
      id: dwy-order-427-1750212812875
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match_preselect_model` 
            MODIFY COLUMN `distribution_result` tinyint(1) NULL DEFAULT NULL COMMENT '分发结果（1：待处理，2：MT想要，3：MT不想要，4：取消分发）' AFTER `shoot_attention`;
      comment: 注释修改
  - changeSet:
      id: dwy-order-428-1751511466490
      author: dwy
      changes:
        - createIndex:
            tableName: order_video
            indexName: idx_status_rt_sc_mt_platform
            unique: false
            columns:
              - column:
                  name: status
              - column:
                  name: release_time
              - column:
                  name: shooting_country
              - column:
                  name: model_type
              - column:
                  name: platform
  - changeSet:
      id: dwy-order-429-1751512403385
      author: dwy
      changes:
        - sql: |
            ALTER TABLE `order_video_match` 
            DROP INDEX `idx_video_id_rollback_id`,
            DROP INDEX `idx_vid_stat_end_start`,
            ADD INDEX `idx_vid_rid_status_et_st`(`video_id` ASC, `rollback_id` ASC, `status`, `end_time`, `start_time`) USING BTREE;
        - createIndex:
            tableName: order_video_match_preselect_model
            indexName: idx_mid_aty_ati_ss_ot_dr
            unique: false
            columns:
              - column:
                  name: match_id
              - column:
                  name: add_type
              - column:
                  name: add_time
              - column:
                  name: select_status
              - column:
                  name: oust_time
              - column:
                  name: distribution_result
  - changeSet:
      id: dwy-order-430-1752802747212
      author: dwy
      changes:
        - createIndex:
            tableName: order_video_logistic
            indexName: idx_vid_st
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: shipping_time
        - createIndex:
            tableName: order_video_model_shipping_address
            indexName: idx_id_vid_rid
            unique: false
            columns:
              - column:
                  name: id
              - column:
                  name: video_id
              - column:
                  name: rollback_id
        - createIndex:
            tableName: order_video_feed_back_material
            indexName: idx_vid_rid
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: rollback_id
        - createIndex:
            tableName: order_video_refund
            indexName: idx_vid_rs_rt_at
            unique: false
            columns:
              - column:
                  name: video_id
              - column:
                  name: refund_status
              - column:
                  name: refund_type
              - column:
                  name: apply_time
        - createIndex:
            tableName: order_video_logistic_follow
            indexName: idx_ovlid_mr
            unique: false
            columns:
              - column:
                  name: order_video_logistic_id
              - column:
                  name: model_result
        - createIndex:
            tableName: order_video_rollback_record_change
            indexName: idx_rid_lmst
            unique: false
            columns:
              - column:
                  name: rollback_id
              - column:
                  name: last_model_submit_time
        - sql: |
            ALTER TABLE `order_video_feed_back` 
            DROP INDEX `order_video_feed_back_video_id_create_time_IDX`,
            ADD INDEX `idx_vid_rid_ct`(`video_id` ASC, `rollback_id`, `create_time` ASC) USING BTREE;
        - sql: |
            ALTER TABLE order_video_match
              ADD COLUMN both_not_null TINYINT(1)
                AS (shoot_model_id IS NOT NULL AND submit_time IS NOT NULL) STORED,
              ADD INDEX idx_both_not_null (both_not_null);