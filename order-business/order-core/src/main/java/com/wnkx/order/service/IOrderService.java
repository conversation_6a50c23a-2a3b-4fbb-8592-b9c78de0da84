package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.PayTranStatusEnum;
import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.FlowMemberDto;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.vo.CheckWechatVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountSimpleVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.finace.*;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Validated
public interface IOrderService extends IService<Order> {
    /**
     * 查询订单列表
     *
     * @param orderListDTO 订单
     * @return 订单集合
     */
    List<OrderListVO> selectOrderListByCondition(OrderListDTO orderListDTO);


    /**
     * 原应收审批接口
     * @param orderListDTO
     * @return
     */
    List<OrderListVO> receivableAuditList(OrderListDTO orderListDTO);
    /**
     * 应收审批-视频订单
     *
     * @param orderListDTO
     * @return
     */
    List<OrderVideoAuditVO> receivableAuditListV1(OrderListDTO orderListDTO);

    /**
     * 应收审批统计
     *
     * @return
     */
    OrderAuditStatusStatisticsVO orderAuditStatusStatistics();


    /**
     * 应收审批未审核数据统计
     *
     * @return
     */
    UnApproveStatusStatistics unApproveStatusStatistics();

    /**
     * 会员审核统计
     *
     * @return
     */
    OrderAuditStatusStatisticsVO orderMemberAuditStatusStatistics();

    /**
     * 应收审批导出*
     *
     * @param list
     * @return
     */
    List<ReceivableAuditListExportVO> exportReceivableAuditList(List<OrderListVO> list);

    /**
     * 获取订单明细列表
     *
     * @param dto
     * @return
     */
    List<OrderPayDetailVO> orderPayDetailList(OrderPayDetailDTO dto);

    /**
     * 获取订单明细基础数据
     *
     * @param dto
     * @return
     */
    List<OrderPayDetailVO> getBasePayDetailVOS(OrderPayDetailDTO dto);

    /**
     * 获取导出订单明细列表数据
     *
     * @param dto
     * @return
     */
    List<OrderPayDetailExportVO> getOrderPayDetailExportVos(OrderPayDetailDTO dto);

    /**
     * 获取视频明细列表
     *
     * @param dto
     * @return
     */
    List<OrderPayVideoDetailVO> orderPayDetailVideoList(OrderPayDetailVideoListDTO dto);

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    OrderVO receivableAuditDetail(Long id);

    /**
     * 获取订单详情
     *
     * @param id
     * @return
     */
    OrderAuditInfoVO receivableAuditInfo(Long id);


    /**
     * 加载凭证
     *
     * @param vo
     * @return
     */
    OrderVO loadPayDocument(OrderVO vo);

    /**
     * 审核订单
     *
     * @param dto
     */
    void auditOrder(OrderAuditDto dto);

    /**
     * 审核合并订单
     *
     * @param dto
     */
    void auditMergeOrder(OrderAuditDto dto);

    /**
     * 创建订单
     *
     * @param orderVideoDTOS 视频订单
     * @return 结果
     */
    CreateOrderVO createOrder(List<OrderVideoDTO> orderVideoDTOS);


    /**
     * 创建订单具体操作
     */
    CreateOrderVO getCreateOrderVO(List<OrderVideoDTO> orderVideoDTOS);

    /**
     * 检查商家状态查看是否登录商家是否能够使用
     */
    void checkBusinessStatus();

    /**
     * 修改视频订单_获取视频详细信息
     *
     * @param videoId 视频订单的主键
     */
    OrderVideoVO getOrderVideoInfo(Long videoId);

    /**
     * 修改视频订单
     */
    void updateOrderVideo(OrderVideoDTO orderVideoDTO);

    /**
     * 查询视频订单匹配情况反馈
     */
    List<OrderVideoCaseVO> selectOrderVideoCaseListByVideoId(Long videoId);

    /**
     * 商家回复匹配情况反馈
     */
    void replyVideoCase(ReplyVideoCaseDTO replyVideoCaseDTO);

    /**
     * 取消订单
     */
    void cancelOrder(String orderNum, boolean isMerge);

    /**
     * 开启订单
     *
     * @param orderNum
     */
    void reopenOrder(String orderNum);

    /**
     * 回滚余额
     *
     * @param orders
     */
    void rollbackBalance(List<Order> orders);

    /**
     * 商家端申请取消会员订单
     */
    void companyApplyCancelMemberOrder(String orderNum);

    /**
     * 加载渠道想
     * @param seedCode
     * @param distributionChannelEntityBySeedCode
     * @param channelBrokeRageVO
     * @return
     */
    PromotionActivityVO loadChannel(String seedCode, DistributionChannel distributionChannelEntityBySeedCode, ChannelBrokeRageVO channelBrokeRageVO);

    /**
     * 取消订单
     *
     * @param orderNum
     */
    void cancelMemberOrder(String orderNum);

    /**
     * 取消订单（子账号入驻页）
     */
    CheckWechatVO cancelMemberOrderBySubAccount(String code, List<String> orderNums, Boolean isCancel);

    /**
     * 标记发货
     */
    void shippingFlag(FlagShippingDTO flagShippingDTO);

    /**
     * 发货
     */
    void shipping(ShippingDTO shippingDTO);

    /**
     * 更换模特
     */
    CreateOrderVO changeModel(Long videoId, Long modelId, String reason);

    /**
     * 确认成品
     */
    void affirmGoods(OrderAffirmDTO dto);

    /**
     * 更新订单状态
     *
     * @param orderNum
     */
    void updateOrderPayStatus(String orderNum, BigDecimal totalAmount, Integer payType, String mchntOrderNo, String mchid);

    /**
     * 商家端-订单各个状态统计
     */
    OrderStatusVO merchantOrderStatusCount();

    /**
     * 加入购物车
     */
    CreateOrderVO addCart(OrderVideoDTO orderVideoDTO);

    /**
     * 购物车列表
     */
    List<VideoCartVO> selectCartList(CartListDTO cartListDTO);

    /**
     * 删除购物车订单
     */
    void deleteCart(List<Long> cartIds);

    /**
     * 查看购物车订单
     */
    VideoCartVO getCartInfo(Long cartId);

    /**
     * 购物车结算
     */
    CreateOrderVO cartSettleAccounts(List<CartSettleAccountsDTO> cartSettleAccountsDTOS);

    /**
     * 确认模特
     */
    void confirmModel(Long id);

    /**
     * 编辑购物车
     */
    CreateOrderVO editCart(OrderVideoDTO orderVideoDTO);

    /**
     * 运营端-订单各个状态统计
     */
    OrderStatusVO backOrderStatusCount();


    /**
     * 商家端工作台统计
     * @return
     */
    OrderStatusVO workbenchOrderStatusCount();

    /**
     * 运营发起反馈
     */
    void sendOrderVideoCase(SendVideoCaseDTO sendVideoCaseDTO);

    /**
     * 运营修改订单费用
     */
    void updateOrderVideoPrice(UpdateOrderVideoPriceDTO dto);

    /**
     * 运营_修改视频订单_获取视频详细信息
     *
     * @param videoId 视频订单的主键
     */
    OrderOperationVideoVO getOperationOrderVideoInfo(Long videoId, OrderListDTO dto);

    /**
     * 审核订单
     */
    CreateOrderVO editOperationOrderVideoInfo(OrderOperationVideoDTO orderOperationVideoDTO);

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @return 模特id
     */
    List<Long> checkAbnormalModelId(Collection<Long> modelId);

    /**
     * 校验模特是否可以下单 or 接单
     *
     * @param modelIds  模特id
     * @param bizUserId 订单下单登录账号ID
     * @return 无法接单的模特id
     */
    public Collection<Long> checkModelCanAccept(Collection<Long> modelIds, Long bizUserId);

    /**
     * 提交凭证信息
     */
    void submitCredential(SubmitCredentialDTO submitCredentialDTO, Boolean isAnother);


    /**
     * 获取收款账号列表
     *
     * @param orderNums
     * @return
     */
    List<OrderPayeeAccount> queryOrderPayeeAccountListByOrderNums(List<String> orderNums);

    /**
     * 下单锁定
     *
     * @param dto
     */
    void payLock(OrderPayLockDTO dto);

    /**
     * 分配视频订单使用余额
     *
     * @param orderNums
     * @param useBalance
     * @return
     */
    List<OrderVideo> assignVideoBalance(List<Order> orders, BigDecimal useBalance);

    /**
     * 代付下单锁定
     */
    void anotherPayLock(OrderPayLockDTO dto);

    /**
     * 释放订单锁定数据
     */
    void releasePayLock(List<Order> orders);

    /**
     * 余额支付
     *
     * @param dto
     */
    void balancePay(@Valid BalancePayDTO dto);

    /**
     * 根据订单号查询订单
     */
    Order getOrderByOrderNum(String orderNum);

    /**
     * 上传产品图
     */
    void uploadProductImage(Long videoId, String productPic);


    /**
     * 获取订单统计
     *
     * @param orderVideoStatisticsDTO
     * @return
     */
    OrderVideoStatisticsVO orderVideoStatistics(OrderVideoStatisticsDTO orderVideoStatisticsDTO);


    /**
     * 获取订单详情统计
     *
     * @param orderVideoStatisticsDTO
     * @return
     */
    List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(OrderVideoStatisticsDTO orderVideoStatisticsDTO);


    /**
     * 余额修改
     *
     * @param businessBalanceDto
     */
    void updateBusinessBalance(BusinessBalanceDTO businessBalanceDto);

    /**
     * 会员状态流转 内置余额修改
     *
     * @param dto
     */
    BusinessAccountVO flowMember(FlowMemberDto dto);

    /**
     * 根据条件查询视频订单列表
     */
    List<OrderVideo> selectOrderVideoListByCondition(OrderVideoListDTO orderVideoListDTO);

    /**
     * 订单列表-获取拍摄模特下拉框
     */
    List<ModelInfoVO> orderShootModelSelect(String keyword);

    /**
     * 订单列表-获取对接人下拉框
     */
    List<UserVO> orderContactSelect(String keyword);


    /**
     * 订单列表-获取出单人下拉框
     */
    List<UserVO> orderIssueSelect(String keyword);


    /**
     * 订单列表-获取下单用户下拉框（商家端）
     */
    Set<String> companyOrderUserSelect(String keyword);

    /**
     * 订单列表-获取下单用户下拉框（运营端）
     */
    List<BusinessAccountSimpleVO> backOrderUserSelect(String keyword);

    /**
     * 获取订单总数
     */
    Long getOrderCount();

    /**
     * 根据登录账号ID获取会员有效账号数量
     *
     * @param bizUserId
     * @return
     */
    Long getValidOrderMemberCount(Long bizUserId);

    /**
     * 获取未取消订单数量*
     *
     * @param businessId
     * @return
     */
    Long getUnCancelOrderCount(Long businessId);

    /**
     * 运营根据匹配情况修改订单
     */
    void operationVideoCase(OrderOperationVideoCaseDTO dto);

    /**
     * 当前商户购物车数量统计
     */
    Long getCartCount();

    /**
     * 批量更新视频订单的对接人
     */
    Boolean updateOrderVideoContact(UpdateOrderContactDTO dto);

    /**
     * 复制购物车
     */
    VideoCartVO copyCart(Long cartId);


    /**
     * 购物车列表-下单运营下拉框
     */
    List<BusinessAccountSimpleVO> cartCreateUserSelect(String keyword);

    /**
     * 接收抓取亚马逊图片更新视频订单
     */
    void updateBatchOrderVideoProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto);

    /**
     * 接收抓取亚马逊图片更新购物车订单
     */
    void updateBatchOrderCartProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto);

    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    List<ModelOrderVO> getModelOrderCount(Collection<Long> modelIds);

    /**
     * 获取模特超时率、售后率
     */
    List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate();

    void createOrderFlow(List<OrderVideo> orderVideos, OrderStatusEnum orderStatus, String eventName);

    void createOrderFlow(OrderVideo orderVideo, OrderStatusEnum orderStatus, String eventName);

    /**
     * 运营帮商家上传素材
     */
    void backHelpUploadMaterial(OrderVideoUploadLinkDTO dto);

    /**
     * 订单详情-变更记录
     */
    List<OrderVideoChangeLogVO> getVideoHistoryChangeRecord(Long videoId);

    /**
     * 重新加入购物车
     */
    void rejoinCart(Long videoId);

    /**
     * 视频订单发货信息
     */
    ShippingVO shippingInfo(Long videoId);

    /**
     * 更改购物车意向模特
     */
    CreateOrderVO updateCartIntentionModel(UpdateCartIntentionModelDTO dto);

    /**
     * 根据订单金额计算公式 获取订单最终金额
     */
    BigDecimal getOrderFinalAmount(Order order);

    /**
     * 查看视频订单_视频详细信息（商家端）
     */
    OrderVideoDetailVO getOrderVideoDetailCompanyInfo(Long videoId, Boolean isBusiness);

    /**
     * 查看视频订单_视频详细信息（运营端）
     */
    OrderVideoDetailVO getOrderVideoDetailBackInfo(Long videoId, Boolean isBusiness);

    /**
     * 确认收货
     *
     * @param logisticId
     * @param signTime
     */
    void confirmReceipt(Long logisticId, Date signTime);

    /**
     * 视频订单导出（运营端）
     */
    void orderVideoExport(OrderListDTO orderListDTO, HttpServletResponse response);

    /**
     * 视频订单导出（商家端）
     */
    void orderVideoCompanyExport(OrderListDTO orderListDTO, HttpServletResponse response);

    /**
     * 检查当前订单是否还需支付
     */
    PayTranStatusEnum checkOrderNeedPay(CheckStatusDTO checkStatusDTO);

    PayTranStatusEnum checkOrderAnotherPay(CheckStatusDTO checkStatusDTO);

    /**
     * 爬取亚马逊图片写入视频订单表
     */
    void crawlTask(AsyncCrawlTask asyncCrawlTask);

    /**
     * 校验意向模特是否符合订单信息
     */
    List<Long> checkModelMeetOrder(List<Long> modelIds, OrderVideoDTO orderVideoDTO);

    /**
     * 汇率异常调整
     */
    void updateBaiduRate(UpdateBaiduRateDTO dto);

    /**
     * 获取订单列表视频订单统计数量
     */
    Integer videoStatistics(OrderListDTO orderListDTO);

    /**
     * 获取余额锁定记录
     *
     * @return
     */
    List<BalanceLockRecordVO> getBalanceLockRecord();

    /**
     * 获取视频订单列表
     *
     * @return
     */
    List<OrderListVO> getOrderList(OrderListDTO orderListDTO);

    /**
     * 关闭过往订单
     */
    void cancelLatestUnPayOrderNumber();

    List<String> selectOrderUserNicknameList(String name);

    /**
     * 获取待完成、需确认状态下的订单的出单人信息
     */
    List<UnFinishedAndNeedConfirmOrderIssueSelectVO> unFinishedAndNeedConfirmOrderIssueSelect();

    /**
     * 运营手动获取产品图
     */
    void crawlProductPic(Long videoId);

    /**
     * @param dto
     */
    OrderPayLog saveOrderPayLog(@Valid OrderPayLogDTO dto);

    /**
     * 禁用会员订单发票
     *
     * @param businessId
     */
    void banMemberInvoice(Long businessId);

    /**
     * 保存收款账号
     *
     * @param dto
     */
    void saveOrderPayeeAccount(OrderPayAccountDTO dto);

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    void updateIssueId(UpdateIssueIdDTO dto);

    /**
     * 订单回退
     */
    void rollbackOrder(RollbackOrderDTO dto);

    /**
     * * 导出财务对账
     *
     * @param dto
     * @param response
     */
    void exportFinancialVerificationList(FinancialVerificationExportDTO dto, HttpServletResponse response);


    /**
     * 清楚购物车意向模特
     *
     * @param dto
     */
    void clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto);

    /**
     * 商家端-发票管理-未开票列表
     */
    List<CompanyNotInvoicedListVO> selectCompanyNotInvoicedListByCondition(CompanyNotInvoicedListDTO dto);

    /**
     * 通过订单号查询订单
     */
    List<Order> selectListByOrderNums(Collection<String> orderNums);

    List<OrderVideoRefund> getOrderVideoRefundList(List<String> numbers);

    List<Order> getOrderPayAppIdInfo(List<String> orderNums);

    void updateWechatPayAppId(List<String> orderNums, OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO);

    void updateAliPayAppId(List<String> orderNums, OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO);

    /**
     * 清除支付类型
     */
    void clearPayType(List<String> orderNums);

    /**
     * 释放所使用的余额
     */
    void releaseUseBalance(List<String> orderNums);

    /**
     * 合并时设置订单支付号
     */
    void setPayNum(List<String> orderNums, String payNum);

    /**
     * 提交支付时设置订单支付号
     */
    void setPayNum(String orderNum);

    /**
     * 组装查询列表的条件
     *
     * @param orderListDTO 列表参数
     */
    Boolean wrapperCondition(OrderListDTO orderListDTO);

    /**
     * 获取订单列表数据
     */
    List<OrderListVO> selectOrderListVOS(OrderListDTO orderListDTO);

    /**
     * 取消合并时清空订单支付号
     */
    void emptyPayNum(List<String> orderNums);

    /**
     * 获取用户账号创建订单数量
     */
    Long getUserAccountCreateOrderCount();

    /**
     * 获取财务视频待审核
     * @return
     */
    List<OrderVideoAuditVO> workbenchFinanceVideoList();

    OrderVideoRefundSimpleListVO getVideoRefundMap(List<Long> videoIds);

    Map<Long, List<VideoTaskOrderVO>> getVideoTaskOrderVOMap(List<Long> videoIds);

    /**
     * 取消会员订单
     */
    void cancelMemberOrders(List<String> orderNums);
}

