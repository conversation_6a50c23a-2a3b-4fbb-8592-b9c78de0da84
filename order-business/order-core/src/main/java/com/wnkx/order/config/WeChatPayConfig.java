package com.wnkx.order.config;

import com.ruoyi.system.api.config.WechatConfig;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 微信支付配置
 * @create :2024-10-24 16:11
 **/
@Slf4j
@Data
public class WeChatPayConfig {
    /**
     * 商户号
     */
    public String appId;

    /**
     * 商户号
     */
    public String merchantId;

    /**
     * 商户APIV3密钥
     */
    public String apiV3Key;

    public String notifyUrl;

    /**
     * 商户证书序列号
     */
    public String merchantSerialNumber;

    /**
     * 私钥
     */
    public String privateKey;

    public NativePayService nativePayService;
    public NotificationParser notificationParser;
    public RSAAutoCertificateConfig rsaAutoCertificateConfig;


}
