package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.wnkx.db.mapper.SuperMapper;
import com.ruoyi.system.api.domain.entity.order.WechatOrderTable;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【wechat_order_table(微信订单表)】的数据库操作Mapper
 * @createDate 2024-10-25 09:28:00
 * @Entity com.ruoyi.system.api.domain.entity.order.WechatOrderTable
 */
public interface WechatOrderTableMapper extends SuperMapper<WechatOrderTable> {

    /**
     * 根据订单号获取有效订单数据
     *
     * @param orderNum
     * @return
     */
    default WechatOrderTable getValidWechatOrderTable(String orderNum) {
        return this.selectOne(new LambdaQueryWrapper<WechatOrderTable>()
                .eq(WechatOrderTable::getOrderNum, orderNum)
                .eq(WechatOrderTable::getStatus, StatusTypeEnum.YES.getCode())
                .last("limit 1")
        );
    }

    /**
     * 根据商户订单号获取数据
     * @param outTradeNo
     * @return
     */
    default WechatOrderTable getByOutTradeNo(String outTradeNo){
        return this.selectOne(new LambdaQueryWrapper<WechatOrderTable>()
                .eq(WechatOrderTable::getMchntOrderNo, outTradeNo)
        );

    }

    /**
     * 获取有效订单列表
     *
     * @param orderNum
     * @return
     */
    default List<WechatOrderTable> getValidFyOrderTableListByOrderNum(String orderNum) {
        return this.selectList(new LambdaQueryWrapper<WechatOrderTable>()
                .eq(WechatOrderTable::getOrderNum, orderNum)
                .eq(WechatOrderTable::getStatus, StatusTypeEnum.YES.getCode())
        );

    }

    List<WechatOrderTable> unPayAlipayOrderList();
}




