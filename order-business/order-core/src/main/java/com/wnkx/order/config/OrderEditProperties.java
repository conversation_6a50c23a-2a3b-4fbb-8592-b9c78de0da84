package com.wnkx.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Component
@ConfigurationProperties(prefix = "order.edit")
@Data
@RefreshScope
public class OrderEditProperties {

    /**
     * 剪辑管理上传账号
     */
    private List<String> uploadAccount;

    /**
     * 剪辑管理旧数据结束时间
     */
    private String oldDataEndTime;
}
