package com.wnkx.order.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Component
@ConfigurationProperties(prefix = "pay.alipay")
@Data
@Slf4j
public class AlipayProperties {

    /**
     * appId
     */
    private String appId;

    /**
     * 商家账号（PID）
     */
    private String sellerId;

    /**
     * 支付宝网关地址
     */
    private String serverUrl;

    /**
     * 支付宝回调地址
     */
    private String notifyUrl;

    /**
     * 支付宝支付成功后跳转地址-视频订单
     */
    private String returnUrlVideo;

    /**
     * 支付宝支付成功后跳转地址-会员订单
     */
    private String returnUrlMember;

    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;

    /**
     * 应用私钥
     */
    private String privateKey;

    /**
     * 应用公钥
     */
    private String applicationPublicKey;
    private AlipayClient alipayClient;
    private AlipayConfig alipayConfig;
}
