package com.wnkx.order.service.core.impl.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.enums.OrderTaskStatusEnum;
import com.ruoyi.common.core.enums.OrderTaskTypeEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoTaskFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTask;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import com.wnkx.order.service.OrderVideoTaskService;
import com.wnkx.order.service.core.OrderVideoTaskFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 已关闭
 * @create :2024-12-11 16:53
 **/
@Service
@RequiredArgsConstructor
public class CloseTaskServiceImpl implements OrderVideoTaskFlowService {

    private final OrderVideoTaskService orderVideoTaskService;
    @Override
    public void flowVideoTask(OrderVideoTaskFlowDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = dto.getOrderVideoTaskDetail();
        OrderVideoTask orderVideoTask = orderVideoTaskService.getById(orderVideoTaskDetail.getTaskId());
        Assert.notNull(orderVideoTask, "工单不存在");
        if (OrderTaskTypeEnum.AFTER_SALE.getCode().equals(orderVideoTask.getTaskType())) {
            Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus())
                    || OrderTaskStatusEnum.APPLICATION_FOR_CANCELLATION.getCode().equals(orderVideoTaskDetail.getStatus())
                    || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(orderVideoTaskDetail.getStatus())
                    , "任务单状态异常，需要是[待处理]或者[申请取消中]或者[处理中]");
        } else {
            Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus()), "任务单状态异常，需要是[待处理]");
        }

        orderVideoTaskDetail.setEndTime(DateUtil.date());
        orderVideoTaskDetail.setStatus(OrderTaskStatusEnum.CLOSE.getCode());
    }

    @Override
    public OrderTaskStatusEnum getOrderTaskStatusEnumType() {
        return OrderTaskStatusEnum.CLOSE;
    }
}
