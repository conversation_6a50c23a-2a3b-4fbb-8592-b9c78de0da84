package com.wnkx.order.service.core.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频订单流转至交易关闭
 *
 * <AUTHOR>
 * @date 2024/6/29 10:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowTradeClose implements OrderVideoFlowService {

    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoTaskService orderVideoTaskService;
    private final RemoteService remoteService;
    private final OrderVideoMatchService orderVideoMatchService;
    @Override
    public void orderVideoFlow(List<OrderVideo> orderVideos, String eventName) {
        //  ->交易关闭
        //  a:订单状态修改

        Set<Long> createOrderUserIds = orderVideos.stream().map(OrderVideo::getCreateOrderUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, createOrderUserIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.TRADE_CLOSE.getCode()).build());

            orderVideo.setStatus(OrderStatusEnum.TRADE_CLOSE.getCode());
            orderVideo.setStatusTime(DateUtil.date());

            //  存储下单运营快照数据
            orderVideo.setCreateOrderUserName(accountMap.getOrDefault(orderVideo.getCreateOrderUserId(), new BusinessAccountDetailVO()).getName());
            orderVideo.setCreateOrderUserNickName(accountMap.getOrDefault(orderVideo.getCreateOrderUserId(), new BusinessAccountDetailVO()).getNickName());
        }
        List<Long> videoIds = orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList());
        orderVideoFlowRecordService.createOrderVideoFlow(eventName, orderVideoFlowDTOS);
        orderVideoFlowNodeDiagramService.terminateOrderVideoFlowNode(videoIds);
        SpringUtils.getBean(OrderVideoLogisticFollowService.class).deleteByVideoIds(videoIds);

        // 设置模特选择记录的状态为卖方取消
        orderVideoMatchService.updateModelSelectStatusToCancelByVideoId(videoIds);

        //  订单关闭时关闭任务单
        orderVideoTaskService.closeTaskByVideoIds(videoIds, OrderTaskDetailFlowOperateTypeEnum.CLOSE_AFTER_SALE);
    }

    @Override
    public OrderStatusEnum getOrderStatusType() {
        return OrderStatusEnum.TRADE_CLOSE;
    }
}
