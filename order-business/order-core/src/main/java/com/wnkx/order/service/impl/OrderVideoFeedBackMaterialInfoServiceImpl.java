package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.config.OrderEditProperties;
import com.wnkx.order.mapper.OrderVideoFeedBackMaterialInfoMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单反馈(模特)_详细信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
@RequiredArgsConstructor
public class OrderVideoFeedBackMaterialInfoServiceImpl extends ServiceImpl<OrderVideoFeedBackMaterialInfoMapper, OrderVideoFeedBackMaterialInfo> implements IOrderVideoFeedBackMaterialInfoService {

    private final RemoteService remoteService;
    private final OrderResourceService orderResourceService;
    private final IOrderService orderService;
    private final RedisService redisService;
    private final IOrderVideoService orderVideoService;
    private final OrderVideoFeedBackMaterialInfoTaskDetailService orderVideoFeedBackMaterialInfoTaskDetailService;
    private final OrderVideoFeedBackService orderVideoFeedBackService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderEditProperties orderEditProperties;
    private final IOrderVideoTaskDetailService orderVideoTaskDetailService;

    /**
     * 获取存在剪辑任务为待下载、待反馈、待剪辑、需确认、待上传 中的视频订单ID
     */
    @Override
    public List<Long> getInTheEditingVideoIds(List<Long> videoIds) {
        return baseMapper.getInTheEditingVideoIds(videoIds);
    }

    /**
     * 通过模特反馈素材ID关闭模特反馈素材详情
     */
    @Override
    public void closeMaterialInfoByMaterialIds(List<Long> materialIds, EditCloseReasonEnum editCloseReasonEnum) {
        List<OrderVideoFeedBackMaterialInfo> orderVideoFeedBackMaterialInfos = baseMapper.selectListByMaterialId(materialIds);
        if (CollUtil.isEmpty(materialIds)) {
            return;
        }

        DateTime date = DateUtil.date();
        Long userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();
        Integer loginUserType = SecurityUtils.getLoginUserType();

        for (OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo : orderVideoFeedBackMaterialInfos) {
            if (MaterialInfoStatusEnum.CLOSED.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus())) {
                continue;
            }
            if (!EditCloseReasonEnum.ORDER_ROLLBACK.getCode().equals(orderVideoFeedBackMaterialInfo.getFeedbackStatus())
                    && !FeedBackStatusEnum.ALREADY_FED_BACK.getCode().equals(orderVideoFeedBackMaterialInfo.getFeedbackStatus())) {
                orderVideoFeedBackMaterialInfo.setFeedbackStatus(editCloseReasonEnum.getCode());
            }

            orderVideoFeedBackMaterialInfo.setStatus(MaterialInfoStatusEnum.CLOSED.getCode());
            orderVideoFeedBackMaterialInfo.setStatusTime(date);
            orderVideoFeedBackMaterialInfo.setCloseBy(username);
            orderVideoFeedBackMaterialInfo.setCloseById(userId);
            orderVideoFeedBackMaterialInfo.setCloseByType(loginUserType);
            orderVideoFeedBackMaterialInfo.setCloseReason(editCloseReasonEnum.getCode());
        }
        baseMapper.updateBatchById(orderVideoFeedBackMaterialInfos);
    }

    @Override
    public WorkbenchVO getWorkbenchStatistics() {
        return baseMapper.getWorkbenchStatistics(orderEditProperties.getOldDataEndTime());
    }

    /**
     * 剪辑管理-已关闭 列表
     */
    @Override
    public List<ClosedListVO> selectClosedListByCondition(ClosedListDTO dto) {
        if (CharSequenceUtil.isNotBlank(dto.getKeyword())) {
            List<SysUser> userList = remoteService.getUserList(SysUserListDTO.builder().userName(dto.getKeyword()).build());
            dto.setBackUserIds(userList.stream().map(SysUser::getUserId).collect(Collectors.toSet()));

            List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(ModelListDTO.builder().name(dto.getKeyword()).build());
            dto.setModelIds(modelOrderSimpleVOS.stream().map(ModelOrderSimpleVO::getId).collect(Collectors.toSet()));
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ct.status_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<ClosedListVO> closedListVOS = baseMapper.selectClosedListByCondition(dto);

        if (CollUtil.isEmpty(closedListVOS)) {
            return closedListVOS;
        }

        List<String> referencePicIds = closedListVOS.stream().map(ClosedListVO::getReferencePicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        Set<Long> shootModelIds = closedListVOS.stream().map(ClosedListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        Set<Long> contactIds = closedListVOS.stream().map(ClosedListVO::getContactId).collect(Collectors.toSet());
        Set<Long> issueIds = closedListVOS.stream().map(ClosedListVO::getIssueId).collect(Collectors.toSet());
        Set<Long> backCloseByIds = closedListVOS.stream().filter(item -> ObjectUtil.equals(item.getCloseByType(), UserTypeConstants.MANAGER_TYPE) || ObjectUtil.isNull(item.getCloseByType())).map(ClosedListVO::getCloseById).collect(Collectors.toSet());
        contactIds.addAll(issueIds);
        contactIds.addAll(backCloseByIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());

        Set<Long> companyCloseByIds = closedListVOS.stream().filter(item -> ObjectUtil.equals(item.getCloseByType(), UserTypeConstants.USER_TYPE)).map(ClosedListVO::getCloseById).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, companyCloseByIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        OrderVideoRefundSimpleListVO videoRefundMap = orderService.getVideoRefundMap(closedListVOS.stream().map(ClosedListVO::getVideoId).collect(Collectors.toList()));

        for (ClosedListVO closedListVO : closedListVOS) {
            //  参考图片
            List<Long> referencePicIdList = StringUtils.splitToLong(closedListVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicIdList) {
                OrderResource resource = resourceMap.get(referencePicId);
                if (resource != null) {
                    closedListVO.getReferencePic().add(resource.getObjectKey());
                }
            }
            //  订单退款信息
            closedListVO.setOrderVideoRefund(videoRefundMap.getMapVo().get(closedListVO.getVideoId()));
            closedListVO.setOrderVideoRefundList(videoRefundMap.getListVo().get(closedListVO.getVideoId()));

            closedListVO.setShootModel(modelSimpleMap.get(closedListVO.getShootModelId()));

            closedListVO.setContact(userMap.get(closedListVO.getContactId()));
            closedListVO.setIssue(userMap.get(closedListVO.getIssueId()));
            if (ObjectUtil.equals(closedListVO.getCloseByType(), UserTypeConstants.MANAGER_TYPE) || ObjectUtil.isNull(closedListVO.getCloseByType())) {
                closedListVO.setCloseBy(userMap.getOrDefault(closedListVO.getCloseById(), new UserVO()).getName());
            } else {
                closedListVO.setCloseBy(accountMap.getOrDefault(closedListVO.getCloseById(), new BusinessAccountDetailVO()).getName());
            }
        }

        return closedListVOS;
    }

    /**
     * 剪辑管理-需确认列表-导出
     */
    @Override
    public void exportNeedConfirmList(MaterialInfoListDTO dto, HttpServletResponse response) {
        Assert.isTrue(ObjectUtil.isNotNull(dto.getFeedbackTimeBegin()) && ObjectUtil.isNotNull(dto.getFeedbackTimeEnd()), "请选择反馈时间后再导出~");
        List<ExportNeedConfirmListVO> exportNeedConfirmListVOS = baseMapper.selectExportNeedConfirmList(dto.getFeedbackTimeBegin(), dto.getFeedbackTimeEnd());

        Set<Long> shootModelIds = exportNeedConfirmListVOS.stream().map(ExportNeedConfirmListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        for (ExportNeedConfirmListVO exportNeedConfirmListVO : exportNeedConfirmListVOS) {
            exportNeedConfirmListVO.setShootModelName(modelSimpleMap.getOrDefault(exportNeedConfirmListVO.getShootModelId(), new ModelOrderSimpleVO()).getName());
            exportNeedConfirmListVO.setModelType(modelSimpleMap.getOrDefault(exportNeedConfirmListVO.getShootModelId(), new ModelOrderSimpleVO()).getType());
            exportNeedConfirmListVO.setCreateOrderUserName(exportNeedConfirmListVO.getCreateOrderUserName() + StrPool.SLASH + exportNeedConfirmListVO.getCreateOrderUserNickName());
        }

        ExcelUtil<ExportNeedConfirmListVO> util = new ExcelUtil<>(ExportNeedConfirmListVO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "剪辑管理-需确认列表导出");
        util.exportExcel(response, exportNeedConfirmListVOS, "剪辑管理-需确认列表导出");
    }

    /**
     * 剪辑管理-添加反馈素材给商家
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addFeedBack(OrderVideoFeedBackDTO dto) {
        OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());
        Assert.notNull(orderVideo, "视频订单不存在");
        orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.UN_FINISHED, OrderStatusEnum.NEED_CONFIRM, OrderStatusEnum.FINISHED);
        if (ObjectUtil.isNull(orderVideo.getPicCount())) {
            Assert.isTrue(OrderVideoFeedBackTypeEnum.VIDEO.getCode().equals(dto.getType()), "视频订单没有选配，反馈类型只能选择视频");
        }

        if (ObjectUtil.isNotNull(dto.getVideoScore()) || CharSequenceUtil.isNotBlank(dto.getVideoScoreContent())) {
            Assert.isFalse(baseMapper.checkScored(orderVideo.getId(), orderVideo.getRollbackId()), "视频订单已评分");
        }

        if (CollUtil.isEmpty(orderVideoFeedBackService.getFeedBackList(orderVideo.getId(), orderVideo.getRollbackId()))) {
            //  蜗牛内部只要首次上传了视频素材，订单状态由“待完成”流转至“需确认”
            SpringUtils.getBean(IOrderService.class).createOrderFlow(SpringUtils.getBean(IOrderVideoService.class).getById(dto.getVideoId()), OrderStatusEnum.NEED_CONFIRM, "运营反馈素材给商家");
        }

        DateTime date = DateUtil.date();
        Long userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();

        OrderVideoFeedBack orderVideoFeedBack = BeanUtil.copyProperties(dto, OrderVideoFeedBack.class);

        try {
            if (ObjectUtil.isNotNull(dto.getMaterialInfoId())) {
                Assert.isTrue(redisService.getLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + dto.getMaterialInfoId(), CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");
                OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo = baseMapper.selectById(dto.getMaterialInfoId());
                Assert.notNull(orderVideoFeedBackMaterialInfo, "素材详情不存在");
                Assert.isTrue(MaterialInfoStatusEnum.WAIT_FOR_FEEDBACK.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus()) || MaterialInfoStatusEnum.NEED_TO_CONFIRM.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus()), "素材详情状态异常");

                if (MaterialInfoStatusEnum.WAIT_FOR_FEEDBACK.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus())) {
                    orderVideoFeedBackMaterialInfo.setFeedbackStatus(FeedBackStatusEnum.ALREADY_FED_BACK.getCode());
                    orderVideoFeedBackMaterialInfo.setStatus(MaterialInfoStatusEnum.NEED_TO_CONFIRM.getCode());
                    orderVideoFeedBackMaterialInfo.setStatusTime(date);

                    if (ObjectUtil.isNull(dto.getVideoScore()) && ObjectUtil.isNotNull(orderVideoFeedBackMaterialInfo.getVideoScore())) {
                        orderVideoFeedBack.setVideoScore(orderVideoFeedBackMaterialInfo.getVideoScore());
                        orderVideoFeedBack.setVideoScoreContent(orderVideoFeedBackMaterialInfo.getVideoScoreContent());
                        orderVideoFeedBack.setVideoScoreBy(orderVideoFeedBackMaterialInfo.getVideoScoreBy());
                        orderVideoFeedBack.setVideoScoreById(orderVideoFeedBackMaterialInfo.getVideoScoreById());
                        orderVideoFeedBack.setVideoScoreTime(orderVideoFeedBackMaterialInfo.getVideoScoreTime());
                    }
                }
                orderVideoFeedBackMaterialInfo.setFeedbackBy(username);
                orderVideoFeedBackMaterialInfo.setFeedbackById(userId);
                orderVideoFeedBackMaterialInfo.setFeedbackTime(date);
                baseMapper.updateById(orderVideoFeedBackMaterialInfo);
            }
            if (ObjectUtil.isNotNull(dto.getVideoScore())) {
                orderVideoFeedBack.setVideoScoreBy(username);
                orderVideoFeedBack.setVideoScoreById(userId);
                orderVideoFeedBack.setVideoScoreTime(date);
            }
            orderVideoFeedBack.setFeedbackBy(username);
            orderVideoFeedBack.setFeedbackById(userId);
            orderVideoFeedBack.setFeedbackTime(date);
            orderVideoFeedBack.setRollbackId(orderVideo.getRollbackId());
            orderVideoFeedBack.setCreateUserId(userId);
            orderVideoFeedBack.setCreateBy(username);
            orderVideoFeedBackService.save(orderVideoFeedBack);
            // orderVideoService.setAutoCompleteTime(dto.getVideoId());

            if (ObjectUtil.isNotNull(dto.getMaterialInfoId()) || CollUtil.isNotEmpty(dto.getTaskDetailIds())) {
                List<Long> taskDetailIds = new ArrayList<>();
                List<OrderVideoFeedBackMaterialInfoTaskDetail> saveOrUpdateMaterialInfoTaskDetails = new ArrayList<>();
                //  获取模特反馈素材详情已关联的任务
                if (ObjectUtil.isNotNull(dto.getMaterialInfoId())) {
                    List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails = orderVideoFeedBackMaterialInfoTaskDetailService.selectListByMaterialInfoIdAndVideoIdAndRollbackId(dto.getMaterialInfoId(), orderVideo.getId(), orderVideo.getRollbackId());
                    taskDetailIds.addAll(materialInfoTaskDetails.stream().map(OrderVideoFeedBackMaterialInfoTaskDetail::getTaskDetailId).collect(Collectors.toList()));

                    for (OrderVideoFeedBackMaterialInfoTaskDetail materialInfoTaskDetail : materialInfoTaskDetails) {
                        materialInfoTaskDetail.setFeedBackId(orderVideoFeedBack.getId());
                    }
                    saveOrUpdateMaterialInfoTaskDetails.addAll(materialInfoTaskDetails);
                }

                if (CollUtil.isNotEmpty(dto.getTaskDetailIds())) {
                    List<OrderVideoTaskDetail> orderVideoTaskDetails = orderVideoTaskDetailService.listByIds(dto.getTaskDetailIds());
                    Assert.notEmpty(orderVideoTaskDetails, "选择的任务不存在");
                    Assert.isTrue(orderVideoTaskDetails.size() == dto.getTaskDetailIds().size(), "选择的任务数量与库里对不上");
                    taskDetailIds.addAll(dto.getTaskDetailIds());

                    //  获取需新增的任务
                    List<OrderVideoFeedBackMaterialInfoTaskDetail> saveOrderVideoFeedBackMaterialInfoTaskDetails = new ArrayList<>();
                    for (Long taskDetailId : dto.getTaskDetailIds()) {
                        OrderVideoFeedBackMaterialInfoTaskDetail orderVideoFeedBackMaterialInfoTaskDetail = new OrderVideoFeedBackMaterialInfoTaskDetail();
                        orderVideoFeedBackMaterialInfoTaskDetail.setMaterialInfoId(dto.getMaterialInfoId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setFeedBackId(orderVideoFeedBack.getId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setVideoId(orderVideo.getId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setRollbackId(orderVideo.getRollbackId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setTaskDetailId(taskDetailId);
                        orderVideoFeedBackMaterialInfoTaskDetail.setSubmitBy(username);
                        orderVideoFeedBackMaterialInfoTaskDetail.setSubmitById(userId);
                        saveOrderVideoFeedBackMaterialInfoTaskDetails.add(orderVideoFeedBackMaterialInfoTaskDetail);
                    }
                    saveOrUpdateMaterialInfoTaskDetails.addAll(saveOrderVideoFeedBackMaterialInfoTaskDetails);
                }
                orderVideoFeedBackMaterialInfoTaskDetailService.saveOrUpdateBatch(saveOrUpdateMaterialInfoTaskDetails);
                orderVideoTaskDetailService.finishWorkOrders(taskDetailIds, OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION, TaskDetailFlowRecordOperateByTypeEnum.EDITOR, Collections.emptyList());
            }


            if (ObjectUtil.isNotNull(dto.getModifyId())) {
                OrderVideoFeedBack oldOrderVideoFeedBack = orderVideoFeedBackService.getById(dto.getModifyId());
                Assert.notNull(oldOrderVideoFeedBack, "要修改的记录不存在");
                Assert.isTrue(oldOrderVideoFeedBack.getType().equals(dto.getType()), "反馈类型不能修改");
                Assert.isTrue(StatusTypeEnum.YES.getCode().equals(oldOrderVideoFeedBack.getIsNew()) && CharSequenceUtil.isBlank(oldOrderVideoFeedBack.getModifyReason()), "只能修改一次");
                oldOrderVideoFeedBack.setIsNew(StatusTypeEnum.NO.getCode());
                orderVideoFeedBackService.updateById(oldOrderVideoFeedBack);

                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.MODIFY_MATERIAL_SUBMISSION_MERCHANT.getEventName(),
                        OrderVideoOperateTypeEnum.MODIFY_MATERIAL_SUBMISSION_MERCHANT.getIsPublic(),
                        OrderVideoOperateDTO.builder().videoId(dto.getVideoId()).eventContent(OrderVideoOperateTypeEnum.MODIFY_MATERIAL_SUBMISSION_MERCHANT.getEventContent()).build()
                );
            } else {
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.MATERIAL_SUBMISSION_MERCHANT.getEventName(),
                        OrderVideoOperateTypeEnum.MATERIAL_SUBMISSION_MERCHANT.getIsPublic(),
                        OrderVideoOperateDTO.builder().videoId(dto.getVideoId()).eventContent(OrderVideoOperateTypeEnum.MATERIAL_SUBMISSION_MERCHANT.getEventContent()).build()
                );
            }
        } finally {
            if (ObjectUtil.isNotNull(dto.getMaterialInfoId())) {
                redisService.releaseLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + dto.getMaterialInfoId());
            }
        }
    }

    /**
     * 校验能否评分
     */
    @Override
    public Boolean checkCanScore(Long videoId) {
        OrderVideo orderVideo = orderVideoService.getById(videoId);
        Assert.notNull(orderVideo, "数据不存在");

        return !baseMapper.checkScored(videoId, orderVideo.getRollbackId());
    }

    /**
     * 剪辑管理-待反馈、需确认 剪辑人下拉框
     */
    @Override
    public List<UserVO> selectEditPerson(Integer status) {
        List<Long> editByIds = baseMapper.getEditPerson(status);
        if (CollUtil.isEmpty(editByIds)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(editByIds);
        List<SysUser> userList = remoteService.getUserList(dto);

        List<UserVO> userVOS = new ArrayList<>();

        userList.forEach(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getUserId());
            userVO.setName(user.getUserName());
            userVO.setPhonenumber(user.getPhonenumber());
            userVOS.add(userVO);

        });

        return userVOS;
    }

    /**
     * 剪辑管理-待下载 领取人下拉框
     */
    @Override
    public List<UserVO> selectReceivePerson() {
        List<Long> getByIds = baseMapper.getReceivePerson();
        if (CollUtil.isEmpty(getByIds)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(getByIds);
        List<SysUser> userList = remoteService.getUserList(dto);

        List<UserVO> userVOS = new ArrayList<>();

        userList.forEach(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getUserId());
            userVO.setName(user.getUserName());
            userVO.setPhonenumber(user.getPhonenumber());
            userVOS.add(userVO);

        });

        return userVOS;
    }

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 拍摄模特下拉框
     */
    @Override
    public List<ModelInfoVO> selectShootModel(Integer status) {
        List<Long> shootModelIds = baseMapper.getShootModelId(status, orderEditProperties.getOldDataEndTime());
        if (CollUtil.isEmpty(shootModelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(shootModelIds);
        return remoteService.innerList(modelListDTO);
    }

    /**
     * 剪辑管理-不反馈给商家
     */
    @Override
    public void markNoFeedback(MarkNoFeedbackDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + dto.getId(), CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");

        try {
            OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo = baseMapper.selectById(dto.getId());
            Assert.notNull(orderVideoFeedBackMaterialInfo, "数据不存在");
            Assert.isTrue(MaterialInfoStatusEnum.TO_BE_EDITED.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus())
                    || MaterialInfoStatusEnum.WAIT_FOR_FEEDBACK.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus())
                    || MaterialInfoStatusEnum.TO_BE_DOWNLOADED.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus()), "请刷新页面后重试~");

            DateTime date = DateUtil.date();

            orderVideoFeedBackMaterialInfo.setFeedbackBy(SecurityUtils.getUsername());
            orderVideoFeedBackMaterialInfo.setFeedbackById(SecurityUtils.getUserId());
            orderVideoFeedBackMaterialInfo.setFeedbackTime(date);
            orderVideoFeedBackMaterialInfo.setFeedbackStatus(FeedBackStatusEnum.NO_FEEDBACK_TO_THE_MERCHANT.getCode());
            orderVideoFeedBackMaterialInfo.setCloseReason(EditCloseReasonEnum.NO_FEEDBACK_TO_THE_MERCHANT.getCode());
            orderVideoFeedBackMaterialInfo.setFeedbackRemark(dto.getFeedbackRemark());
            orderVideoFeedBackMaterialInfo.setStatus(MaterialInfoStatusEnum.CLOSED.getCode());
            orderVideoFeedBackMaterialInfo.setStatusTime(date);
            orderVideoFeedBackMaterialInfo.setCloseBy(SecurityUtils.getUsername());
            orderVideoFeedBackMaterialInfo.setCloseById(SecurityUtils.getUserId());
            baseMapper.updateById(orderVideoFeedBackMaterialInfo);
        } finally {
            redisService.releaseLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + dto.getId());
        }
    }

    /**
     * 剪辑管理-标记已剪辑
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markClip(MarkClipDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + dto.getId(), CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");

        try {
            OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo = baseMapper.selectById(dto.getId());
            Assert.notNull(orderVideoFeedBackMaterialInfo, "数据不存在");
            Assert.isTrue(MaterialInfoStatusEnum.TO_BE_EDITED.getCode().equals(orderVideoFeedBackMaterialInfo.getStatus()), "请刷新页面后重试~");

            OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());
            Assert.notNull(orderVideo, "视频订单不存在");

            if (ObjectUtil.isNotNull(dto.getVideoScore()) || CharSequenceUtil.isNotBlank(dto.getVideoScoreContent())) {
                Assert.isFalse(baseMapper.checkScored(orderVideo.getId(), orderVideo.getRollbackId()), "视频订单已评分");
            }

            DateTime date = DateUtil.date();
            String username = SecurityUtils.getUsername();
            Long userId = SecurityUtils.getUserId();

            orderVideoFeedBackMaterialInfo.setEditBy(username);
            orderVideoFeedBackMaterialInfo.setEditById(userId);
            orderVideoFeedBackMaterialInfo.setEditTime(date);
            orderVideoFeedBackMaterialInfo.setVideoScore(dto.getVideoScore());
            orderVideoFeedBackMaterialInfo.setVideoScoreContent(dto.getVideoScoreContent());
            orderVideoFeedBackMaterialInfo.setStatus(MaterialInfoStatusEnum.WAIT_FOR_FEEDBACK.getCode());
            orderVideoFeedBackMaterialInfo.setStatusTime(date);
            if (ObjectUtil.isNotNull(dto.getVideoScore())) {
                orderVideoFeedBackMaterialInfo.setVideoScoreBy(username);
                orderVideoFeedBackMaterialInfo.setVideoScoreById(userId);
                orderVideoFeedBackMaterialInfo.setVideoScoreTime(date);
            }
            baseMapper.updateById(orderVideoFeedBackMaterialInfo);

            if (CollUtil.isNotEmpty(dto.getTaskDetailIds())) {
                List<OrderVideoTaskDetail> orderVideoTaskDetails = orderVideoTaskDetailService.listByIds(dto.getTaskDetailIds());
                Assert.notEmpty(orderVideoTaskDetails, "选择的任务不存在");
                Assert.isTrue(orderVideoTaskDetails.size() == dto.getTaskDetailIds().size(), "选择的任务数量与库里对不上");
                //  获取视频订单已关联的任务
                List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails = orderVideoFeedBackMaterialInfoTaskDetailService.selectListByMaterialInfoIdAndVideoIdAndRollbackId(null, orderVideo.getId(), orderVideo.getRollbackId());
                List<Long> taskDetailIds = materialInfoTaskDetails.stream().map(OrderVideoFeedBackMaterialInfoTaskDetail::getTaskDetailId).collect(Collectors.toList());
                //  获取需新增的任务
                List<Long> subtractToList = CollUtil.subtractToList(dto.getTaskDetailIds(), taskDetailIds);
                if (CollUtil.isNotEmpty(subtractToList)) {
                    List<OrderVideoFeedBackMaterialInfoTaskDetail> saveOrderVideoFeedBackMaterialInfoTaskDetails = new ArrayList<>();
                    for (Long taskDetailId : subtractToList) {
                        OrderVideoFeedBackMaterialInfoTaskDetail orderVideoFeedBackMaterialInfoTaskDetail = new OrderVideoFeedBackMaterialInfoTaskDetail();
                        orderVideoFeedBackMaterialInfoTaskDetail.setMaterialInfoId(orderVideoFeedBackMaterialInfo.getId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setVideoId(orderVideo.getId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setRollbackId(orderVideo.getRollbackId());
                        orderVideoFeedBackMaterialInfoTaskDetail.setTaskDetailId(taskDetailId);
                        orderVideoFeedBackMaterialInfoTaskDetail.setSubmitBy(username);
                        orderVideoFeedBackMaterialInfoTaskDetail.setSubmitById(userId);
                        saveOrderVideoFeedBackMaterialInfoTaskDetails.add(orderVideoFeedBackMaterialInfoTaskDetail);
                    }
                    orderVideoFeedBackMaterialInfoTaskDetailService.saveBatch(saveOrderVideoFeedBackMaterialInfoTaskDetails);
                }
            }
        } finally {
            redisService.releaseLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + dto.getId());
        }
    }

    /**
     * 剪辑管理-历史剪辑要求
     */
    @Override
    public HistoryClipRecordVO getHistoryClipRecord(Long videoId) {
        OrderVideo orderVideo = orderVideoService.getById(videoId);
        Assert.notNull(orderVideo, "数据不存在");

        HistoryClipRecordVO historyClipRecordVO = new HistoryClipRecordVO();
        historyClipRecordVO.setOrderVideoVO(BeanUtil.copyProperties(orderVideo, OrderVideoVO.class));

        List<HistoryClipRecordListVO> historyClipRecordListVOS = baseMapper.getHistoryClipRecord(videoId);
        if (CollUtil.isEmpty(historyClipRecordListVOS)) {
            return historyClipRecordVO;
        }

        Set<Long> submitByIds = historyClipRecordListVOS.stream().map(HistoryClipRecordListVO::getSubmitById).collect(Collectors.toSet());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(submitByIds).build());

        List<String> issuePicIds = historyClipRecordListVOS.stream().map(HistoryClipRecordListVO::getIssuePicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(issuePicIds, StrUtil.COMMA));
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        for (HistoryClipRecordListVO historyClipRecordListVO : historyClipRecordListVOS) {
            historyClipRecordListVO.setSubmitBy(userMap.getOrDefault(historyClipRecordListVO.getSubmitById(), new UserVO()).getName());

            if (CharSequenceUtil.isNotBlank(historyClipRecordListVO.getIssuePicId())) {
                List<Long> issuePicIdList = StringUtils.splitToLong(historyClipRecordListVO.getIssuePicId(), StrUtil.COMMA);
                for (Long issuePicId : issuePicIdList) {
                    OrderResource resource = resourceMap.get(issuePicId);
                    if (resource != null) {
                        historyClipRecordListVO.getIssuePic().add(resource.getObjectKey());
                    }
                }
            }
        }

        historyClipRecordVO.setHistoryClipRecordListVOS(historyClipRecordListVOS);
        return historyClipRecordVO;
    }

    /**
     * 剪辑管理-标记下载
     */
    @Override
    public void markDownload(Long id) {
        Assert.isTrue(redisService.getLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + id, CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");

        try {
            OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo = baseMapper.selectById(id);
            Assert.notNull(orderVideoFeedBackMaterialInfo, "数据不存在");
            Assert.isTrue(StatusTypeEnum.YES.getCode().equals(orderVideoFeedBackMaterialInfo.getGetStatus()), "请先领取素材~");
            Assert.isTrue(StatusTypeEnum.NO.getCode().equals(orderVideoFeedBackMaterialInfo.getDownloadFlag()), "素材已下载，请刷新页面~");

            DateTime date = DateUtil.date();
            orderVideoFeedBackMaterialInfo.setDownloadFlag(StatusTypeEnum.YES.getCode());
            orderVideoFeedBackMaterialInfo.setDownloadFlagTime(date);
            orderVideoFeedBackMaterialInfo.setStatus(MaterialInfoStatusEnum.TO_BE_EDITED.getCode());
            orderVideoFeedBackMaterialInfo.setStatusTime(date);
            baseMapper.updateById(orderVideoFeedBackMaterialInfo);
        } finally {
            redisService.releaseLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + id);
        }
    }

    /**
     * 剪辑管理-标记下载-校验是否存在进行中的任务
     */
    @Override
    public Boolean checkDownload(Long videoId) {
        return baseMapper.checkDownload(videoId);
    }

    /**
     * 剪辑管理-领取
     */
    @Override
    public void getEdit(GetEditDTO dto) {
        MaterialInfoListDTO materialInfoListDTO = new MaterialInfoListDTO();
        materialInfoListDTO.setGetStatus(StatusTypeEnum.NO.getCode());
        materialInfoListDTO.setIds(dto.getIds());
        materialInfoListDTO.setStatus(MaterialInfoStatusEnum.TO_BE_DOWNLOADED.getCode());
        List<MaterialInfoListVO> materialInfoListVOS = baseMapper.selectMaterialInfoListByCondition(materialInfoListDTO, orderEditProperties.getOldDataEndTime());
        Assert.notEmpty(materialInfoListVOS, "请刷新列表后重试~");
        if (Boolean.FALSE.equals(dto.getSelectAll())) {
            Assert.isTrue(dto.getIds().size() == materialInfoListVOS.size(), "请刷新列表后重试~");
        }

        List<Long> lockIds = new ArrayList<>();
        try {
            for (MaterialInfoListVO materialInfoListVO : materialInfoListVOS) {
                Assert.isTrue(redisService.getLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + materialInfoListVO.getId(), CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");
                lockIds.add(materialInfoListVO.getId());
            }
        } finally {
            for (Long lockId : lockIds) {
                redisService.releaseLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + lockId);
            }
        }

        //  获取今日领取编码最大值
        Integer todayMaxGetCode = baseMapper.getTodayMaxGetCode();

        List<OrderVideoFeedBackMaterialInfo> updateMaterialInfoList = new ArrayList<>();

        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();

        try {
            for (MaterialInfoListVO materialInfoListVO : materialInfoListVOS) {
                OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo = new OrderVideoFeedBackMaterialInfo();
                orderVideoFeedBackMaterialInfo.setId(materialInfoListVO.getId());
                orderVideoFeedBackMaterialInfo.setGetCode(++todayMaxGetCode);
                orderVideoFeedBackMaterialInfo.setGetBy(username);
                orderVideoFeedBackMaterialInfo.setGetById(userId);
                orderVideoFeedBackMaterialInfo.setGetTime(date);
                orderVideoFeedBackMaterialInfo.setGetStatus(StatusTypeEnum.YES.getCode());
                updateMaterialInfoList.add(orderVideoFeedBackMaterialInfo);
            }
            baseMapper.updateBatchById(updateMaterialInfoList);
        } finally {
            for (Long id : lockIds) {
                redisService.releaseLock(CacheConstants.MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY + id);
            }
        }
    }

    /**
     * 剪辑管理-历史剪辑记录
     */
    @Override
    public List<SelectHistoryEditListVO> selectHistoryEditList(Long videoId) {
        List<SelectHistoryEditListVO> list = baseMapper.selectHistoryEditList(videoId);
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        List<Long> materialInfoIds = list.stream().map(SelectHistoryEditListVO::getId).collect(Collectors.toList());
        List<OrderVideoFeedBack> feedBacks = orderVideoFeedBackService.selectListByMaterialInfoIds(materialInfoIds);
        Set<Long> orderVideoFeedBackByIds = feedBacks.stream().map(OrderVideoFeedBack::getFeedbackById).collect(Collectors.toSet());
        Map<Long, List<OrderVideoFeedBack>> feedBackMap = feedBacks.stream().collect(Collectors.groupingBy(OrderVideoFeedBack::getMaterialInfoId));

        Set<Long> uploadByIds = list.stream().map(SelectHistoryEditListVO::getUploadById).collect(Collectors.toSet());
        Set<Long> getByIds = list.stream().map(SelectHistoryEditListVO::getGetById).collect(Collectors.toSet());
        Set<Long> editByIds = list.stream().map(SelectHistoryEditListVO::getEditById).collect(Collectors.toSet());
        Set<Long> feedbackByIds = list.stream().map(SelectHistoryEditListVO::getFeedbackById).collect(Collectors.toSet());

        uploadByIds.addAll(getByIds);
        uploadByIds.addAll(editByIds);
        uploadByIds.addAll(feedbackByIds);
        uploadByIds.addAll(orderVideoFeedBackByIds);

        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(uploadByIds).build());

        for (SelectHistoryEditListVO selectHistoryEditListVO : list) {
            selectHistoryEditListVO.setGetCode(formatGetCode(selectHistoryEditListVO.getGetCode()));
            selectHistoryEditListVO.setUploadBy(userMap.getOrDefault(selectHistoryEditListVO.getUploadById(), new UserVO()).getName());
            selectHistoryEditListVO.setGetBy(userMap.getOrDefault(selectHistoryEditListVO.getGetById(), new UserVO()).getName());
            selectHistoryEditListVO.setEditBy(userMap.getOrDefault(selectHistoryEditListVO.getEditById(), new UserVO()).getName());
            if (FeedBackStatusEnum.ALREADY_FED_BACK.getCode().equals(selectHistoryEditListVO.getFeedbackStatus())) {
                selectHistoryEditListVO.setFeedBackVOS(BeanUtil.copyToList(feedBackMap.get(selectHistoryEditListVO.getId()), SelectHistoryEditListVOFeedBackVO.class));
            } else {
                selectHistoryEditListVO.setFeedBackVOS(List.of(SelectHistoryEditListVOFeedBackVO.builder().feedbackById(selectHistoryEditListVO.getFeedbackById()).feedbackTime(selectHistoryEditListVO.getFeedbackTime()).build()));
            }

            if (CollUtil.isNotEmpty(selectHistoryEditListVO.getFeedBackVOS())) {
                for (SelectHistoryEditListVOFeedBackVO feedBackVO : selectHistoryEditListVO.getFeedBackVOS()) {
                    feedBackVO.setFeedbackBy(userMap.getOrDefault(feedBackVO.getFeedbackById(), new UserVO()).getName());
                }
            }
        }

        return list;
    }

    /**
     * 剪辑管理-待下载列表统计
     */
    @Override
    public GetMaterialInfoStatisticsVO getMaterialInfoStatistics() {
        return baseMapper.getMaterialInfoStatistics(orderEditProperties.getOldDataEndTime());
    }

    /**
     * 剪辑管理-待下载、待剪辑、待反馈、需确认 列表
     */
    @Override
    public List<MaterialInfoListVO> selectMaterialInfoListByCondition(MaterialInfoListDTO dto) {
        dto.setGetCode(removeLeadingZeros(dto.getGetCode()));
        if (CharSequenceUtil.isNotBlank(dto.getKeyword())) {
            List<SysUser> userList = remoteService.getUserList(SysUserListDTO.builder().userName(dto.getKeyword()).build());
            dto.setBackUserIds(userList.stream().map(SysUser::getUserId).collect(Collectors.toSet()));
        }

        if (ObjectUtil.isNotNull(dto.getModelType())) {
            List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(ModelListDTO.builder().type(List.of(dto.getModelType())).build());
            dto.getShootModelIds().addAll(modelOrderSimpleVOS.stream().map(ModelOrderSimpleVO::getId).collect(Collectors.toSet()));
        }

        PageUtils.startPageNoDefault();
        List<MaterialInfoListVO> list = baseMapper.selectMaterialInfoListByCondition(dto, orderEditProperties.getOldDataEndTime());
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        List<String> referencePicIds = list.stream().map(MaterialInfoListVO::getReferencePicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        Set<Long> shootModelIds = list.stream().map(MaterialInfoListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        List<Long> videoIds = list.stream().map(MaterialInfoListVO::getVideoId).collect(Collectors.toList());
        OrderVideoRefundSimpleListVO videoRefundMap = orderService.getVideoRefundMap(videoIds);

        Set<Long> contactIds = list.stream().map(MaterialInfoListVO::getContactId).collect(Collectors.toSet());
        Set<Long> issueIds = list.stream().map(MaterialInfoListVO::getIssueId).collect(Collectors.toSet());
        Set<Long> getByIds = list.stream().map(MaterialInfoListVO::getGetById).collect(Collectors.toSet());
        Set<Long> editByIds = list.stream().map(MaterialInfoListVO::getEditById).collect(Collectors.toSet());
        Set<Long> feedbackByIds = list.stream().map(MaterialInfoListVO::getFeedbackById).collect(Collectors.toSet());
        contactIds.addAll(issueIds);
        contactIds.addAll(getByIds);
        contactIds.addAll(editByIds);
        contactIds.addAll(feedbackByIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());

        List<Long> existClipRequire = baseMapper.getExistClipRequireByVideoIds(videoIds);

        Map<Long, List<VideoTaskOrderVO>> videoTaskOrderVOMap = orderService.getVideoTaskOrderVOMap(videoIds);
        for (MaterialInfoListVO materialInfoListVO : list) {
            materialInfoListVO.setGetCode(formatGetCode(materialInfoListVO.getGetCode()));
            //  参考图片
            List<Long> referencePicIdList = StringUtils.splitToLong(materialInfoListVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicIdList) {
                OrderResource resource = resourceMap.get(referencePicId);
                if (resource != null) {
                    materialInfoListVO.getReferencePic().add(resource.getObjectKey());
                }
            }

            //  订单退款信息
            materialInfoListVO.setOrderVideoRefund(videoRefundMap.getMapVo().get(materialInfoListVO.getVideoId()));
            materialInfoListVO.setOrderVideoRefundList(videoRefundMap.getListVo().get(materialInfoListVO.getVideoId()));

            materialInfoListVO.setShootModel(modelSimpleMap.get(materialInfoListVO.getShootModelId()));

            materialInfoListVO.setContact(userMap.get(materialInfoListVO.getContactId()));
            materialInfoListVO.setIssue(userMap.get(materialInfoListVO.getIssueId()));
            materialInfoListVO.setGetBy(userMap.getOrDefault(materialInfoListVO.getGetById(), new UserVO()).getName());
            materialInfoListVO.setEditBy(userMap.getOrDefault(materialInfoListVO.getEditById(), new UserVO()).getName());
            materialInfoListVO.setFeedbackBy(userMap.getOrDefault(materialInfoListVO.getFeedbackById(), new UserVO()).getName());

            List<VideoTaskOrderVO> videoTaskOrderVOS = videoTaskOrderVOMap.get(materialInfoListVO.getVideoId());
            if (CollUtil.isNotEmpty(videoTaskOrderVOS)) {
                List<VideoTaskOrderVO> afterSaleTaskVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.AFTER_SALE.getCode().equals(item.getTaskType())).collect(Collectors.toList());
                List<VideoTaskOrderVO> workOrderVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.WORK_ORDER.getCode().equals(item.getTaskType())).collect(Collectors.toList());
                //  添加售后单状态
                if (CollUtil.isNotEmpty(afterSaleTaskVOS)) {
                    if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()) || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(item.getStatus()))) {
                        materialInfoListVO.setAfterSaleTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                    } else if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                        materialInfoListVO.setAfterSaleTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                    }
                }

                //  添加工单状态
                if (CollUtil.isNotEmpty(workOrderVOS)) {
                    if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()))) {
                        materialInfoListVO.setWorkOrderTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                    } else if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                        materialInfoListVO.setWorkOrderTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                    }
                }
            }

            //  是否包含剪辑要求
            materialInfoListVO.setIsExistClipRequire(existClipRequire.contains(materialInfoListVO.getVideoId()));
        }

        return list;
    }


    @Override
    public List<MaterialInfoListVO> selectUnGetMaterialList() {
        List<MaterialInfoListVO> list = baseMapper.selectUnGetMaterialList(orderEditProperties.getOldDataEndTime());
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        Set<Long> shootModelIds = list.stream().map(MaterialInfoListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);
        Set<Long> contactIds = list.stream().map(MaterialInfoListVO::getContactId).collect(Collectors.toSet());
        Set<Long> issueIds = list.stream().map(MaterialInfoListVO::getIssueId).collect(Collectors.toSet());
        contactIds.addAll(issueIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());
        for (MaterialInfoListVO materialInfoListVO : list) {
            //  订单退款信息
            materialInfoListVO.setShootModel(modelSimpleMap.get(materialInfoListVO.getShootModelId()));
            materialInfoListVO.setContact(userMap.get(materialInfoListVO.getContactId()));
            materialInfoListVO.setIssue(userMap.get(materialInfoListVO.getIssueId()));
        }
        return list;
    }

    /**
     * 添加数据
     */
    @Override
    public void saveOrderFeedBackMaterialInfo(Long materialId, UploadLinkDTO dto) {
        DateTime date = DateUtil.date();

        OrderVideoFeedBackMaterialInfo orderVideoFeedBackMaterialInfo = new OrderVideoFeedBackMaterialInfo();
        orderVideoFeedBackMaterialInfo.setMaterialId(materialId);

        if (ObjectUtil.isNotNull(dto.getTaskDetailId())) {
            OrderVideoTaskDetail taskDetail = orderVideoTaskDetailService.getById(dto.getTaskDetailId());
            Assert.notNull(taskDetail, "任务详情不存在");
            Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(taskDetail.getStatus()) || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(taskDetail.getStatus()), "请刷新页面后重试~");
            if (OrderTaskAfterSaleVideoTypeEnum.RE_UPLOAD.getCode().equals(taskDetail.getAfterSaleVideoType())) {
                dto.setMaterialInfoStatusEnum(MaterialInfoStatusEnum.WAIT_FOR_FEEDBACK);
            }
        }

        orderVideoFeedBackMaterialInfo.setTaskDetailId(dto.getTaskDetailId());
        orderVideoFeedBackMaterialInfo.setLink(dto.getLink());
        orderVideoFeedBackMaterialInfo.setNote(dto.getNote());
        orderVideoFeedBackMaterialInfo.setUploadTime(date);
        orderVideoFeedBackMaterialInfo.setObject(SecurityUtils.getLoginUserType().equals(UserTypeConstants.MANAGER_TYPE) ? UploadObjectEnum.BACK.getCode() : UploadObjectEnum.MODEL.getCode());
        orderVideoFeedBackMaterialInfo.setUserId(SecurityUtils.getUserId());
        if (ObjectUtil.isNotNull(dto.getMaterialInfoStatusEnum())) {
            orderVideoFeedBackMaterialInfo.setStatus(dto.getMaterialInfoStatusEnum().getCode());
        } else if (ObjectUtil.isNotNull(dto.getShootModelId())) {
            Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(List.of(dto.getShootModelId()));
            ModelOrderSimpleVO modelOrderSimpleVO = modelSimpleMap.get(dto.getShootModelId());
            Assert.notNull(modelOrderSimpleVO, "拍摄模特不存在");
            orderVideoFeedBackMaterialInfo.setStatus(ModelTypeEnum.INFLUENT.getCode().equals(modelOrderSimpleVO.getType()) ? MaterialInfoStatusEnum.WAIT_FOR_FEEDBACK.getCode() : MaterialInfoStatusEnum.TO_BE_DOWNLOADED.getCode());
        }
        if (ObjectUtil.isNull(orderVideoFeedBackMaterialInfo.getStatus()) || ObjectUtil.equals(MaterialInfoStatusEnum.TO_BE_DOWNLOADED.getCode(), orderVideoFeedBackMaterialInfo.getStatus())) {
            orderVideoFeedBackMaterialInfo.setEnterTheDownloadTime(date);
        }
        orderVideoFeedBackMaterialInfo.setStatusTime(date);
        baseMapper.insert(orderVideoFeedBackMaterialInfo);
    }

    /**
     * 通过素材id查询素材信息
     *
     * @param materialIds 素材id
     * @return 素材信息
     */
    @Override
    public List<OrderFeedBackMaterialInfoVO> selectListByMaterialId(List<Long> materialIds) {
        List<OrderVideoFeedBackMaterialInfo> materialInfos = baseMapper.selectListByMaterialId(materialIds);
        if (CollUtil.isEmpty(materialInfos)) {
            return Collections.emptyList();
        }
        List<OrderFeedBackMaterialInfoVO> orderFeedBackMaterialInfoVOS = BeanUtil.copyToList(materialInfos, OrderFeedBackMaterialInfoVO.class);

        List<Long> backUserIds = orderFeedBackMaterialInfoVOS.stream()
                .filter(item -> UploadObjectEnum.BACK.getCode().equals(item.getObject()))
                .map(OrderFeedBackMaterialInfoVO::getUserId)
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());

        List<Long> modelUserIds = orderFeedBackMaterialInfoVOS.stream()
                .filter(item -> UploadObjectEnum.MODEL.getCode().equals(item.getObject()))
                .map(OrderFeedBackMaterialInfoVO::getUserId)
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());

        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(backUserIds).build());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(modelUserIds);

        orderFeedBackMaterialInfoVOS.forEach(item -> {
            if (UploadObjectEnum.BACK.getCode().equals(item.getObject())) {
                item.setBack(userMap.get(item.getUserId()));
            } else {
                item.setModel(modelMap.get(item.getUserId()));
            }
        });
        return orderFeedBackMaterialInfoVOS;
    }

    private String formatGetCode(String getCode) {
        if (CharSequenceUtil.isBlank(getCode)) {
            return null;
        }

        if (getCode.length() == 1) {
            return "00" + getCode;
        } else if (getCode.length() == 2) {
            return "0" + getCode;
        }
        return getCode;
    }

    public static String removeLeadingZeros(String str) {
        if (CharSequenceUtil.isBlank(str)) {
            return null;
        }
        String result = str.replaceFirst("^0+", "");
        return result.isEmpty() ? null : result;
    }
}
