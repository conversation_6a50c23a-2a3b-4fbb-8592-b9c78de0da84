package com.wnkx.order.mapper;

import com.ruoyi.system.api.domain.entity.order.OrderNote;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单_商家开票信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-22
 */
@Mapper
public interface OrderNoteMapper extends SuperMapper<OrderNote>
{
    /**
     * 查询订单_商家开票信息
     * 
     * @param id 订单_商家开票信息主键
     * @return 订单_商家开票信息
     */
    public OrderNote selectOrderNoteById(Long id);

    /**
     * 查询订单_商家开票信息
     * @param orderNum 订单号
     * @return
     */
    OrderNote selectOrderNoteByOrderNum(String orderNum);

    /**
     * 查询订单_商家开票信息列表
     * 
     * @param orderNote 订单_商家开票信息
     * @return 订单_商家开票信息集合
     */
    public List<OrderNote> selectOrderNoteList(OrderNote orderNote);

    /**
     * 新增订单_商家开票信息
     * 
     * @param orderNote 订单_商家开票信息
     * @return 结果
     */
    public int insertOrderNote(OrderNote orderNote);

    /**
     * 修改订单_商家开票信息
     * 
     * @param orderNote 订单_商家开票信息
     * @return 结果
     */
    public int updateOrderNote(OrderNote orderNote);

    /**
     * 删除订单_商家开票信息
     * 
     * @param id 订单_商家开票信息主键
     * @return 结果
     */
    public int deleteOrderNoteById(Long id);

    /**
     * 批量删除订单_商家开票信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderNoteByIds(Long[] ids);

    /**
     * 新增订单开票信息
     */
    default void saveOrderNote(OrderNote orderNote) {
        this.insert(orderNote);
    }
}
