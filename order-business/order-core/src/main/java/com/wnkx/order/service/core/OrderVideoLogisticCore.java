package com.wnkx.order.service.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.FlagShippingDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowVO;
import com.wnkx.order.factory.OrderVideoLogisticFlowServiceFactory;
import com.wnkx.order.mapper.OrderVideoLogisticFollowMapper;
import com.wnkx.order.mapper.OrderVideoLogisticFollowRecordMapper;
import com.wnkx.order.mapper.OrderVideoMapper;
import com.wnkx.order.service.IOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :视频订单物流跟进状态数据
 * @create :2025-04-24 11:51
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderVideoLogisticCore {
    private final OrderVideoLogisticFlowServiceFactory orderVideoLogisticFlowServiceFactory;
    private final RedisService redisService;
    private final OrderVideoLogisticFollowMapper orderVideoLogisticFollowMapper;
    private final OrderVideoLogisticFollowRecordMapper orderVideoLogisticFollowRecordMapper;
    private final OrderVideoMapper orderVideoMapper;


    @Transactional(rollbackFor = Exception.class)
    public void logisticFlow(OrderVideoLogisticFlowDTO dto, FollowStatusEnum followStatus) {
        Assert.notNull(dto, "视频订单物流跟进流转参数不能为空！");
        Assert.notNull(dto.getBusinessId(), "视频订单物流跟进流转参数：商家Id不能为空！");

        if (FollowStatusEnum.SHIP == followStatus) {
            if (ReissueEnum.REISSUE.getCode().equals(dto.getReissue())) {
                //已发货-补发
                dto.setOrderVideoLogisticFollowList(new ArrayList<>());
            } else {
                Assert.notNull(dto.getVideoId(), "视频订单物流跟进流转参数：视频id不能为空！");
                //已发货-正常
                List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.queryListBase(OrderVideoLogisticFollowDTO.builder()
                        .videoId(dto.getVideoId())
                        .logisticStatus(StatusTypeEnum.NO.getCode())
                        .build());
                if (CollUtil.isNotEmpty(orderVideoLogisticFollows)){
                    Assert.isTrue(orderVideoLogisticFollows.size() <= 1, "已发货：最多只能有一个未发货数据~");
                    dto.setOrderVideoLogisticFollow(orderVideoLogisticFollows.get(0));
                }
                dto.setOrderVideoLogisticFollowList(CollUtil.isNotEmpty(orderVideoLogisticFollows) ? orderVideoLogisticFollows : null);
            }
        } else if (FollowStatusEnum.NEED_HANDLE == followStatus && HandleStatusEnum.UN_NOTIFIED == dto.getHandleStatus()) {
            dto.setOrderVideoLogisticFollowList(new ArrayList<>());
        }
        List<String> keys = new ArrayList<>();
        try {
            if (CollUtil.isEmpty(dto.getOrderVideoLogisticFollowList())) {
                String key = "";
                if (ObjectUtil.isNull(dto.getOrderVideoLogisticFollow())) {
                    key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + dto.getBusinessId();
                } else {
                    key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + dto.getBusinessId() + "_" + dto.getOrderVideoLogisticFollow().getId();
                }
                Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                keys.add(key);
            } else {
                for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()) {
                    String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + item.getBusinessId() + "_" + item.getId();
                    Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                    keys.add(key);
                }
            }

        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (String key : keys) {
                redisService.releaseLock(key);
            }
            throw new ServiceException("物流跟进流转中，请稍后重试！");
        }
        try {
            if (CollUtil.isNotEmpty(dto.getOrderVideoLogisticFollowList())){
                for (OrderVideoLogisticFollow item : dto.getOrderVideoLogisticFollowList()){
                    Assert.isFalse(FollowStatusEnum.DELETE.getCode().equals(item.getFollowStatus()), "数据已更新，请刷新页面~");
                }
            }
            if (ObjectUtil.isNotNull(dto.getOrderVideoLogisticFollow())){
                Assert.isFalse(FollowStatusEnum.DELETE.getCode().equals(dto.getOrderVideoLogisticFollow().getFollowStatus()), "数据已更新，请刷新页面~");
            }
            OrderVideoLogisticFlowService orderVideoLogisticFlowService = orderVideoLogisticFlowServiceFactory.getOrderVideoLogisticFlowService(followStatus);
            LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();
            dto.setLoginUserInfoVO(loginUserInfoVo);
            orderVideoLogisticFlowService.OrderVideoLogisticFlow(dto);
        } catch (Exception e) {
            log.error("物流跟进流转中，请稍后重试,{}", e);
            for (String key : keys) {
                redisService.releaseLock(key);
            }
            throw new ServiceException(e.getMessage());
        } finally {
            for (String key : keys) {
                redisService.releaseLock(key);
            }
        }
    }

    /**
     * 标记通知：通知发货、提醒发货、通知地址变更、标记发货已提醒、通知确认模特
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void alertShipping(AlertShippingDTO dto) {
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.selectBatchIds(dto.getIds());
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoLogisticFollows), "视频订单物流跟进数据不存在！");
        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollows.get(0).getVideoId());
        orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollows.get(0).getBusinessId());
        orderVideoLogisticFlowDto.setResourceIds(dto.getResourceIds());
        orderVideoLogisticFlowDto.setRemark(dto.getRemark());
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(orderVideoLogisticFollows);



        Integer handleStatus = orderVideoLogisticFollows.get(0).getHandleStatus();
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollows){
            checkFollowList(orderVideoLogisticFlowDto.getBusinessId(), handleStatus, item);
        }

        checkVideoStatus(orderVideoLogisticFollows);
        if (HandleStatusEnum.UN_NOTIFIED.getCode().equals(handleStatus)) {
            //未通知 设置处理状态为 已通知
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.NOTIFIED);
        } else if (HandleStatusEnum.DELAY_REMINDER.getCode().equals(handleStatus)) {
            //延迟发货提醒
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.DELAY_NOTIFIED);
        } else if (HandleStatusEnum.URGE_SHIPPING_REMINDER.getCode().equals(handleStatus)) {
            //催发货提醒
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.URGE_SHIPPING_NOTIFIED);
        } else if (HandleStatusEnum.ADDRESS_CHANGE_NOTICE.getCode().equals(handleStatus)) {
            //地址变更通知
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.CHANGE_NOTIFIED);
        }  else if (HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER.getCode().equals(handleStatus)) {
            //标记发货提醒
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.NOTIFIED_SHIPPING_NOTIFIED);
        }  else if (HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL.getCode().equals(handleStatus)) {
            //通知确认模特
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.NOTIFIED_CONFIRM_MODEL);
        }   else if (HandleStatusEnum.URGE_CONFIRM_MODEL_UN_NOTIFIED.getCode().equals(handleStatus)) {
            //已通知催确认模特
            orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED);
        } else {
            throw new ServiceException("数据已更新，请刷新页面~");
        }
        logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.TEMP_HOLD);
    }

    /**
     * 标记发货
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void shippingFlag(FlagShippingDTO dto) {
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.queryListBase(OrderVideoLogisticFollowDTO.builder()
                .videoId(dto.getVideoId())
                .logisticStatus(StatusTypeEnum.NO.getCode())
                .build());

        Assert.isTrue(CollUtil.isNotEmpty(orderVideoLogisticFollows), "视频订单物流跟进数据不存在！");
        Assert.isTrue(orderVideoLogisticFollows.size() == 1, "一个视频只能有一条物流跟进数据！");
        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollows.get(0).getVideoId());
        orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollows.get(0).getBusinessId());
        orderVideoLogisticFlowDto.setRemark(dto.getRemark());
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(orderVideoLogisticFollows);

        Integer handleStatus = orderVideoLogisticFollows.get(0).getHandleStatus();
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollows) {
            checkFollowList(orderVideoLogisticFlowDto.getBusinessId(), handleStatus, item);
        }

        checkVideoStatus(orderVideoLogisticFollows);
        orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.NOTIFIED_SHIPPING);
        logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.TEMP_HOLD);
    }
    /**
     * 补充说明：暂不处理、未发货
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void remarkReplenish(AlertShippingDTO dto) {
        Assert.isTrue(StrUtil.isNotBlank(dto.getRemark()), "备注不能为空！");
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.selectBatchIds(dto.getIds());
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoLogisticFollows), "视频订单物流跟进数据不存在！");
        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDto.setResourceIds(dto.getResourceIds());
        orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollows.get(0).getVideoId());
        orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollows.get(0).getBusinessId());
        orderVideoLogisticFlowDto.setRemark(dto.getRemark());
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(orderVideoLogisticFollows);
        Integer handleStatus = orderVideoLogisticFollows.get(0).getHandleStatus();
        orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.REMARK_REPLENISH);

        //设置未发货时间
        for (OrderVideoLogisticFollow item :orderVideoLogisticFollows){
            Assert.isFalse(List.of(HandleStatusEnum.NOTIFIED_SHIPPING.
                    getCode(),HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER.getCode(),HandleStatusEnum.NOTIFIED_SHIPPING_NOTIFIED.getCode()).contains(item.getHandleStatus()), "进入标记发货流程 无法执行补充说明~");
            Assert.isTrue(List.of(HandleStatusEnum.NOTIFIED.getCode(),
                    HandleStatusEnum.DELAY_SHIPPING.getCode(),
                    HandleStatusEnum.DELAY_NOTIFIED.getCode(),
                    HandleStatusEnum.URGE_SHIPPING_NOTIFIED.getCode(),
                    HandleStatusEnum.CHANGE_NOTIFIED.getCode(),
                    HandleStatusEnum.NOTIFIED_CONFIRM_MODEL.getCode(),
                    HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED.getCode(),
                    HandleStatusEnum.REMARK_REPLENISH.getCode()).contains(item.getHandleStatus()), "数据已更新，请刷新页面~");
        }
        checkVideoStatus(orderVideoLogisticFollows);
        logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.TEMP_HOLD);
    }

    private void checkFollowList(Long businessId, Integer handleStatus, OrderVideoLogisticFollow item) {
        Assert.isTrue(StatusTypeEnum.NO.getCode().equals(item.getLogisticStatus()), "订单已发货，请刷新页面~");
        Assert.isTrue(businessId.equals(item.getBusinessId()), "包含多个商家，无法批量标记");
        Assert.isTrue(handleStatus.equals(item.getHandleStatus()), "包含多个处理状态，无法批量标记");
    }

    private void checkVideoStatus(List<OrderVideoLogisticFollow> orderVideoLogisticFollows) {
        List<Long> videoIds = orderVideoLogisticFollows.stream().map(OrderVideoLogisticFollow::getVideoId).collect(Collectors.toList());
        List<OrderVideo> orderVideoList = orderVideoMapper.selectBatchIds(videoIds);
        try {
            for (OrderVideo item : orderVideoList) {
                Assert.isTrue(OrderStatusEnum.NEED_FILLED.getCode().equals(item.getStatus()), "订单状态发生变化，请刷新页面重试~");
            }
        } catch (Exception e) {
            throw new ServiceException("订单状态发生变化，请刷新页面重试~");
        }
    }
    /**
     * 延迟发货:
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void delayShipping(DelayShippingDTO dto) {
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.selectBatchIds(dto.getIds());
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoLogisticFollows), "视频订单物流跟进数据不存在！");
        //需要
        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDto.setResourceIds(dto.getResourceIds());
        orderVideoLogisticFlowDto.setRemark(dto.getRemark());
        orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollows.get(0).getVideoId());
        orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollows.get(0).getBusinessId());
        orderVideoLogisticFlowDto.setIsDefaultLogisticStartTime(dto.getIsDefaultLogisticStartTime());
        orderVideoLogisticFlowDto.setLogisticStartTime(dto.getLogisticStartTime());
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(orderVideoLogisticFollows);
        orderVideoLogisticFlowDto.setHandleStatus(HandleStatusEnum.DELAY_SHIPPING);

        Integer handleStatus = orderVideoLogisticFollows.get(0).getHandleStatus();
        for (OrderVideoLogisticFollow item : orderVideoLogisticFollows){
            Assert.isFalse(List.of(HandleStatusEnum.NOTIFIED_SHIPPING.
                    getCode(),HandleStatusEnum.NOTIFIED_SHIPPING_REMINDER.getCode(),HandleStatusEnum.NOTIFIED_SHIPPING_NOTIFIED.getCode()).contains(item.getHandleStatus()), "进入标记发货流程 无法执行延迟发货~");
            Assert.isFalse(List.of(HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL.getCode(),HandleStatusEnum.URGE_CONFIRM_MODEL_UN_NOTIFIED.getCode()
                    ,HandleStatusEnum.NOTIFIED_CONFIRM_MODEL.getCode(),HandleStatusEnum.URGE_CONFIRM_MODEL_NOTIFIED.getCode()).contains(item.getHandleStatus()), "进入App解说流程 无法执行延迟发货~");
            checkFollowList(orderVideoLogisticFlowDto.getBusinessId(), handleStatus, item);
        }
        checkVideoStatus(orderVideoLogisticFollows);
        logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.TEMP_HOLD);
    }

    /**
     * 模特确认
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void modelConfirm(ModelConfirmDTO dto) {
        if (ModelResultEnum.INQUIRED.getCode().equals(dto.getModelResult())) {
//            Assert.isTrue(StrUtil.isNotBlank(dto.getRemark()), "[询问说明]不能为空");
        }else if (ModelResultEnum.RECEIVED.getCode().equals(dto.getModelResult())){
//            Assert.notNull(dto.getSignTime(), "[实际签收时间]不能为空");
        }else if (ModelResultEnum.LOST.getCode().equals(dto.getModelResult())){
            Assert.isTrue(StrUtil.isNotBlank(dto.getRemark()), "[丢件说明]不能为空");
        }
        OrderVideoLogisticFollow orderVideoLogisticFollow = orderVideoLogisticFollowMapper.selectById(dto.getId());
        Assert.notNull(orderVideoLogisticFollow, "视频订单物流跟进数据不存在！");
        if (ModelResultEnum.WAIT_NOTICE_SHOOTING.getCode().equals(orderVideoLogisticFollow.getModelResult())){
            Assert.isTrue(ModelResultEnum.NOTICE_SHOOTING.getCode().equals(dto.getModelResult()), "APP解说类无法执行此流程！");
            dto.setSignTime(new Date());
        }

        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDto.setResourceIds(dto.getResourceIds());
        orderVideoLogisticFlowDto.setRemark(dto.getRemark());
        orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollow.getBusinessId());
        orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollow.getVideoId());
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollow(orderVideoLogisticFollow);
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(Arrays.asList(orderVideoLogisticFollow));

        orderVideoLogisticFlowDto.setModelResult(dto.getModelResult());
        if (ModelResultEnum.NOTICE_SHOOTING.getCode().equals(dto.getModelResult())){
            orderVideoLogisticFlowDto.setSignTime(dto.getSignTime());
        }
        if (ModelResultEnum.RECEIVED.getCode().equals(dto.getModelResult())) {
            if (LogisticMainStatus.DELIVERED.getLabel().equals(orderVideoLogisticFollow.getLatestMainStatus())){
                orderVideoLogisticFlowDto.setSignTime(orderVideoLogisticFollow.getLogisticUpdateTime());
            } else {
                Assert.notNull(dto.getSignTime(), "[实际签收时间]不能为空");
                orderVideoLogisticFlowDto.setSignTime(dto.getSignTime());
            }

            SpringUtils.getBean(IOrderService.class).confirmReceipt(orderVideoLogisticFollow.getOrderVideoLogisticId(), orderVideoLogisticFlowDto.getSignTime());
        }
        if (ModelResultEnum.INQUIRED.getCode().equals(dto.getModelResult())) {
            logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.MODEL_CONFIRM_PEND);
        } else {
            logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.CLOSE);
        }


    }

    /**
     * 物流跟进
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void logisticFollow(LogisticFollowDTO dto) {
        OrderVideoLogisticFollow orderVideoLogisticFollow = orderVideoLogisticFollowMapper.selectById(dto.getId());
        Assert.notNull(orderVideoLogisticFollow, "视频订单物流跟进数据不存在！");
        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDto.setResourceIds(dto.getResourceIds());
        orderVideoLogisticFlowDto.setRemark(dto.getRemark());
        orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollow.getVideoId());
        orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollow.getBusinessId());
        orderVideoLogisticFlowDto.setOrderVideoLogisticFollow(orderVideoLogisticFollow);
        orderVideoLogisticFlowDto.setIsCallback(dto.getIsCallBack());

        orderVideoLogisticFlowDto.setLatestMainStatus(LogisticMainStatus.getLabelByCode(dto.getLogisticMainStatus()));
        orderVideoLogisticFlowDto.setLogisticUpdateTime(dto.getLogisticUpdateTime());

        if (List.of(FollowStatusEnum.NEED_FOLLOW_UP.getCode(), FollowStatusEnum.NO_FOLLOW_NEED.getCode()).contains(orderVideoLogisticFollow.getFollowStatus())) {
            if (List.of(LogisticMainStatus.AVAILABLE_FOR_PICKUP.getLabel(), LogisticMainStatus.DELIVERY_FAILURE.getLabel(), LogisticMainStatus.DELIVERED.getLabel()).contains(orderVideoLogisticFlowDto.getLatestMainStatus())) {
                //物流状态为：到达待取、投递失败、成功签收 流转为模特待确认
                orderVideoLogisticFlowDto.setModelResult(ModelResultEnum.PENDING.getCode());
                logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.MODEL_CONFIRM_PEND);
            } else {
                logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.NEED_FOLLOW_UP);
            }
        }else {
            throw new ServiceException("数据已更新，请刷新页面~！");
        }
    }

    /**
     * 物流回调
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void logisticFollowNotify(LogisticFollowNotifyDTO dto) {
        try {
            if (StrUtil.equals(dto.getNumber(), "APP")) {
                return;
            }
            List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.queryListBase(OrderVideoLogisticFollowDTO.builder()
                    .number(dto.getNumber())
                    .build());
            log.debug("物流回调数据：{}", JSON.toJSONString(dto));
            if (CollUtil.isEmpty(orderVideoLogisticFollows)) {
                return;
            }
            log.debug("当前登录数据：{}", JSON.toJSONString(SecurityUtils.getLoginUserInfoVo()));
            if (StrUtil.isBlank(dto.getLogisticMainStatus()) || StrUtil.isBlank(LogisticMainStatus.getSketchByLabel(dto.getLogisticMainStatus()))) {
                dto.setLogisticMainStatus(LogisticMainStatus.NOT_FOUND.getLabel());
            }

            for (OrderVideoLogisticFollow orderVideoLogisticFollow : orderVideoLogisticFollows) {
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollow.getVideoId());
                orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollow.getBusinessId());
                orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(Arrays.asList(orderVideoLogisticFollow));
                orderVideoLogisticFlowDto.setIsCallback(StatusTypeEnum.YES.getCode());

                orderVideoLogisticFlowDto.setLatestMainStatus(dto.getLogisticMainStatus());
                orderVideoLogisticFlowDto.setLogisticUpdateTime(dto.getLogisticUpdateTime());
                orderVideoLogisticFlowDto.setOrderVideoLogisticFollow(orderVideoLogisticFollow);

                if (List.of(FollowStatusEnum.SHIP.getCode(),
                        FollowStatusEnum.NEED_FOLLOW_UP.getCode(),
                        FollowStatusEnum.MODEL_CONFIRM_PEND.getCode(),
                        FollowStatusEnum.NO_FOLLOW_NEED.getCode()).contains(orderVideoLogisticFollow.getFollowStatus())) {
                    logisticFollowStatus(orderVideoLogisticFlowDto);
                } else if (FollowStatusEnum.CLOSE.getCode().equals(orderVideoLogisticFollow.getFollowStatus())) {
                    if (StatusTypeEnum.YES.getCode().equals(orderVideoLogisticFollow.getIsCallBack())) {
                        String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + orderVideoLogisticFollow.getBusinessId() + "_" + orderVideoLogisticFollow.getId();
                        try {
                            Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                            OrderVideoLogisticFollow build = OrderVideoLogisticFollow.builder()
                                    .id(orderVideoLogisticFollow.getId())
                                    .logisticUpdateTime(dto.getLogisticUpdateTime())
                                    .latestMainStatus(dto.getLogisticMainStatus())
                                    .build();
                            if (LogisticMainStatus.DELIVERED.getLabel().equals(dto.getLogisticMainStatus())) {
                                if (ObjectUtil.isNotNull(orderVideoLogisticFollow.getOrderVideoLogisticId())) {
                                    SpringUtils.getBean(IOrderService.class).confirmReceipt(orderVideoLogisticFollow.getOrderVideoLogisticId(), orderVideoLogisticFlowDto.getSignTime());
                                }
                                build.setSignTime(dto.getLogisticUpdateTime());
                            }
                            orderVideoLogisticFollowMapper.updateById(build);

                        } finally {
                            redisService.releaseLock(key);
                        }
                    }
                } else {
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("物流回调异常：{}", e);
            throw new ServiceException("数据已更新，请刷新页面~！");
        }
    }

    /**
     * 订单回退
     * @param videoId
     */
    @Transactional(rollbackFor = Exception.class)
    public void logisticFollowRollback(Long videoId) {
        try {
            List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.queryListBase(OrderVideoLogisticFollowDTO.builder()
                    .videoId(videoId)
                    .build());

            if (CollUtil.isEmpty(orderVideoLogisticFollows)) {
                return;
            }
            //未发货列表
            List<OrderVideoLogisticFollow> unShipList = new ArrayList<>();

            //已发货列表
            List<OrderVideoLogisticFollow> shippingList = new ArrayList<>();
            for (OrderVideoLogisticFollow orderVideoLogisticFollow : orderVideoLogisticFollows) {
                if (StatusTypeEnum.NO.getCode().equals(orderVideoLogisticFollow.getLogisticStatus())) {
                    unShipList.add(orderVideoLogisticFollow);
                } else if (List.of(FollowStatusEnum.NEED_FOLLOW_UP.getCode(), FollowStatusEnum.MODEL_CONFIRM_PEND.getCode(),
                        FollowStatusEnum.NO_FOLLOW_NEED.getCode(), FollowStatusEnum.SHIP.getCode()).contains(orderVideoLogisticFollow.getFollowStatus())) {
                    shippingList.add(orderVideoLogisticFollow);
                }
            }

            if (CollUtil.isNotEmpty(unShipList)) {
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDto.setVideoId(unShipList.get(0).getVideoId());
                orderVideoLogisticFlowDto.setBusinessId(unShipList.get(0).getBusinessId());
                orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(unShipList);
                logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.DELETE);
            }
            if (CollUtil.isNotEmpty(shippingList)) {
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDto.setVideoId(videoId);
                orderVideoLogisticFlowDto.setModelResult(ModelResultEnum.ROLLBACK.getCode());
                orderVideoLogisticFlowDto.setBusinessId(shippingList.get(0).getBusinessId());
                orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(shippingList);
                logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.CLOSE);
            }
        } catch (Exception e) {
            log.error("物流回调异常：{}", e);
            throw new ServiceException("物流回调异常");
        }

    }

    /**
     * 确认模特
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirmModel(Long videoId){
        List<OrderVideoLogisticFollow> orderVideoLogisticFollows = orderVideoLogisticFollowMapper.queryListBase(OrderVideoLogisticFollowDTO.builder()
                .videoId(videoId)
                .logisticStatus(StatusTypeEnum.NO.getCode())
                .build());
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoLogisticFollows), "当前订单没有物流跟进数据");
        Assert.isTrue(orderVideoLogisticFollows.size() == 1, "只能有一条物流跟进数据");
        OrderVideoLogisticFollow orderVideoLogisticFollow = orderVideoLogisticFollows.get(0);
        String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + orderVideoLogisticFollow.getBusinessId() + "_" + orderVideoLogisticFollow.getId();
        try {
            Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
            Date date = new Date();
            orderVideoLogisticFollowMapper.updateById(OrderVideoLogisticFollow.builder()
                    .id(orderVideoLogisticFollow.getId())
                    .logisticUpdateTime(date)
                    .latestMainStatus(LogisticMainStatus.NOTICE_SHOOTING.getLabel())
                    .followStatus(FollowStatusEnum.MODEL_CONFIRM_PEND.getCode())
                    .logisticStatus(FollowStatusEnum.MODEL_CONFIRM_PEND.getLogisticStatus())
                    .modelResult(ModelResultEnum.WAIT_NOTICE_SHOOTING.getCode())
                    .number("APP")
                    .build());
            LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();
            OrderVideoLogisticFollowRecord orderVideoLogisticFollowRecord = new OrderVideoLogisticFollowRecord();
            orderVideoLogisticFollowRecord.setFollowId(orderVideoLogisticFollow.getId());
            orderVideoLogisticFollowRecord.setEventName("商家已确认");
            orderVideoLogisticFollowRecord.setEventContent(loginUserInfoVo.getName() + "确认模特拍摄");

            orderVideoLogisticFollowRecord.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderVideoLogisticFollowRecord.setEventExecuteUserId(loginUserInfoVo.getUserId());
            orderVideoLogisticFollowRecord.setEventExecuteUserName(loginUserInfoVo.getName());
            orderVideoLogisticFollowRecord.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderVideoLogisticFollowRecord.setEventExecuteTime(new Date());
            orderVideoLogisticFollowRecordMapper.insert(orderVideoLogisticFollowRecord);
        } finally {
            redisService.releaseLock(key);
        }
    }

    /**
     * 模特更新地址通知
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void modelUpdateAddress(ModelUpdateAddressDTO dto) {
        try {
            List<OrderVideoLogisticFollowVO> orderVideoLogisticFollowVOS = orderVideoLogisticFollowMapper.selectOrderVideoLogisticFollowList(OrderVideoLogisticFollowListDTO.builder()
                    .shootModelIdList(Arrays.asList(dto.getModelId()))
                    .logisticStatus(StatusTypeEnum.NO.getCode())
                    .build());

            if (CollUtil.isEmpty(orderVideoLogisticFollowVOS)) {
                return;
            }
            for (OrderVideoLogisticFollowVO item :orderVideoLogisticFollowVOS){
                if (FollowStatusEnum.DELETE.getCode().equals(item.getFollowStatus())) {
                    continue;
                }
                OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                orderVideoLogisticFlowDTO.setBusinessId(item.getBusinessId());
                orderVideoLogisticFlowDTO.setMemberCode(item.getMemberCode());
                orderVideoLogisticFlowDTO.setVideoCode(item.getVideoCode());
                orderVideoLogisticFlowDTO.setVideoId(item.getId());
                orderVideoLogisticFlowDTO.setModelName(dto.getModelName());
                orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.ADDRESS_CHANGE_NOTICE);
                orderVideoLogisticFlowDTO.setOrderVideoLogisticFollow(BeanUtil.copyProperties(item, OrderVideoLogisticFollow.class));
                logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
            }
        } catch (Exception e) {
            log.error("物流回调异常：{}", e);
            throw new ServiceException("物流回调异常");
        }

    }

    /**
     * 根据视频订单id获取 物流跟进流水数据
     * @param videoIds
     * @return
     */
    public List<OrderVideoLogisticFollow> getOrderVideoLogisticFollowListByVideoIds(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return orderVideoLogisticFollowMapper.queryListBase(OrderVideoLogisticFollowDTO.builder().videoIds(videoIds).build());
    }


    private void logisticFollowStatus(OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto) {
        if (List.of(LogisticMainStatus.NOT_FOUND.getLabel(), LogisticMainStatus.EXPIRED.getLabel(), LogisticMainStatus.EXCEPTION.getLabel()).contains(orderVideoLogisticFlowDto.getLatestMainStatus())) {
            //物流状态为：查询不到、运输过久、可能异常 流转为需跟进
            logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.NEED_FOLLOW_UP);
        } else if (List.of(LogisticMainStatus.AVAILABLE_FOR_PICKUP.getLabel(), LogisticMainStatus.DELIVERY_FAILURE.getLabel(), LogisticMainStatus.DELIVERED.getLabel()).contains(orderVideoLogisticFlowDto.getLatestMainStatus())) {
            //物流状态为：到达待取、投递失败、成功签收 流转为模特待确认
            orderVideoLogisticFlowDto.setModelResult(ModelResultEnum.PENDING.getCode());
            logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.MODEL_CONFIRM_PEND);
        } else {
            //物流状态为：运输中 流转为无需跟进
            logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.NO_FOLLOW_NEED);
        }
    }
}
