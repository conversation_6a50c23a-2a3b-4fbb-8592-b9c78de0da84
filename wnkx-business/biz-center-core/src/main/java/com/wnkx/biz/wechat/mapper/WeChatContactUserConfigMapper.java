package com.wnkx.biz.wechat.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【we_chat_contact_user_config(对外联系人配置表)】的数据库操作Mapper
 * @createDate 2025-03-05 13:46:50
 * @Entity com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfig
 */
public interface WeChatContactUserConfigMapper extends BaseMapper<WeChatContactUserConfig> {

    default boolean userIdInDb(String contactUserId) {
        return selectCount(new LambdaQueryWrapper<WeChatContactUserConfig>().eq(WeChatContactUserConfig::getContactUserId, contactUserId)) > 0;
    }

    default List<WeChatContactUserConfig> configList(){
        return selectList(new LambdaQueryWrapper<WeChatContactUserConfig>()
                .select(WeChatContactUserConfig::getId,
                        WeChatContactUserConfig::getContactUserId,
                        WeChatContactUserConfig::getContactUserName)
        );
    }

    String getAcquisitionByContactUserId(@Param("contactUserId") String contactUserId);
}




