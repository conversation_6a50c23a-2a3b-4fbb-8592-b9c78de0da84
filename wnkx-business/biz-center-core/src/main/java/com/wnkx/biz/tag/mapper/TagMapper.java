package com.wnkx.biz.tag.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.system.api.domain.dto.biz.tag.TagListDTO;
import com.ruoyi.system.api.domain.entity.biz.tag.Tag;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 标签Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Mapper
public interface TagMapper extends SuperMapper<Tag>
{
    /**
     * 获取当前分类的一级二级标签
     */
    List<TagListVO> stair(@Param("categoryId") Long categoryId);

    /**
     * 根据条件查询标签列表
     *
     * @param tagListDTO 条件dto
     * @return 标签列表
     */
    default List<Tag> selectListByCondition(TagListDTO tagListDTO) {
        return this.selectList(new LambdaQueryWrapper<Tag>()
                .eq(Tag::getCategoryId, tagListDTO.getCategoryId())
                .like(StrUtil.isNotBlank(tagListDTO.getName()), Tag::getName, tagListDTO.getName())
                .in(CollUtil.isNotEmpty(tagListDTO.getStatus()), Tag::getStatus, tagListDTO.getStatus()));
    }

    /**
     * 通过主键id查询列表
     *
     * @param id 主键id
     */
    default List<Tag> selectByIds(Collection<Long> id) {
        return this.selectList(new LambdaQueryWrapper<Tag>()
                .in(Tag::getId, id));
    }

    /**
     * 获取当前分类的指定级别标签
     */
    List<TagListVO> rank(@Param("categoryId") Long categoryId, @Param("rank") Integer rank);

    /**
     * 获取指定分类的指定级别及以上标签
     */
    List<TagListVO> rankUpslope(@Param("categoryId") Long categoryId, @Param("rank") Integer rank);

    /**
     * 获取下级标签数量
     *
     * @param tag 标签
     * @return 下级标签数量
     */
    default Long getUnderLayerTagCount(Tag tag) {
        return this.selectCount(new LambdaQueryWrapper<Tag>()
                .likeRight(Tag::getPath, tag.getPath() + StrUtil.SLASH + tag.getId())
        );
    }

    /**
     * 获取下级有效标签数量
     *
     * @param tag 标签
     * @return 下级标签数量
     */
    default Long getUnderLayerValidTagCount(Tag tag) {
        return this.selectCount(new LambdaQueryWrapper<Tag>()
                .likeRight(Tag::getPath, tag.getPath() + StrUtil.SLASH + tag.getId())
                .eq(Tag::getStatus, StatusEnum.ENABLED.getCode())
        );
    }

    /**
     * 获取标签下拉框
     */
    default List<Tag> tagSelect(Long categoryId) {
        return selectList(new LambdaQueryWrapper<Tag>()
                .eq(Tag::getCategoryId, categoryId)
                .eq(Tag::getStatus, StatusEnum.ENABLED.getCode())
                .orderByDesc(Tag::getCreateTime)
        );
    }

    /**
     * 获取分组下最大标签
     * @param parentId
     * @return
     */
    default Tag getMaxGroupSort(Long parentId){
        return this.selectOne(new LambdaQueryWrapper<Tag>()
                .eq(Tag::getParentId, parentId)
                .orderByDesc(Tag::getSort)
                .last("limit 1")
        );

    }

    /**
     * 获取同一标签分组下所有的标签
     * * @param parentId
     * @return
     */
    default List<Tag> tagSelectByParentId(Long parentId, Long categoryId, Long startSort, Long endSort) {
        return selectList(new LambdaQueryWrapper<Tag>()
                .eq(Tag::getParentId, parentId)
                .eq(Tag::getCategoryId, categoryId)
                .between(Tag::getSort, startSort, endSort)
                .orderByAsc(Tag::getSort)
        );
    }
}
