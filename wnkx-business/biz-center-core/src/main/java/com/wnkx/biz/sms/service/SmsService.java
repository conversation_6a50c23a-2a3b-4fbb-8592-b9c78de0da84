package com.wnkx.biz.sms.service;

/**
 * 短信验证服务
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
public interface SmsService {

    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @return 验证码
     */
    String sendCode(String phone);


    /**
     * 验证验证码
     *
     * @param phone 手机号
     * @param code  验证码
     * @return 是否验证通过
     */
    boolean verifyCode(String phone, String code);

    /**
     * 获取已经发送验证码的次数
     * @param phone 手机号
     */
    Integer getSendCodeCount(String phone);

}
