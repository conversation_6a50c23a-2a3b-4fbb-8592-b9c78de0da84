package com.wnkx.biz.wechat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.WxConstant;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.config.WorkWeChatConfig;
import com.ruoyi.system.api.domain.dto.biz.wechat.*;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactConfig;
import com.wnkx.biz.wechat.service.WeChatContactConfigService;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkWechatApiServiceImpl implements WorkWechatApiService {
    private final WorkWeChatConfig workWeChatConfig;
    private final RedisService redisService;
    private final OkHttpClient okHttpClient;
    private final WeChatContactConfigService weChatContactConfigService;
    private static final MediaType CONTENT_TYPE_JSON = MediaType.parse("application/json; charset=utf-8");


    /**
     * 获取企业微信ACCESS_TOKEN
     *
     * @return ACCESS_TOKEN
     */
    private String getWorkWxAccessToken() {
        // 优先从缓存中获取，token有效期为7200s = 2hour
        final String cacheToken = redisService.getCacheObject(CacheConstants.WORK_WX_CACHE_ACCESS_TOKEN);
        if (StringUtils.isBlank(cacheToken)) {
            final Request request = new Request.Builder()
                    .url(String.format(WxConstant.GET_TOKEN_URL,
                            workWeChatConfig.getCorpId(),
                            workWeChatConfig.getCorpSecret())).get()
                    .build();
            final String result = requestServer(okHttpClient.newCall(request));
            log.debug("获取企业微信ACCESS_TOKEN:{}", result);
            final String token = JSON.parseObject(result).getString(CommonConstant.ACCESS_TOKEN);
            redisService.setCacheObject(CacheConstants.WORK_WX_CACHE_ACCESS_TOKEN, token, 7100L);
            return token;
        }
        return cacheToken;
    }

    @Override
    public ExternalUserInfoDTO getExternalUserInfo(String externalUserId) {
        final String workWxAccessToken = getWorkWxAccessToken();
        final Request request = new Request.Builder()
                .url(String.format(WxConstant.GET_EXTERNAL_USER_INFO_URL, workWxAccessToken, externalUserId)).get()
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("获取外部联系人信息{}", result);
        return JSON.parseObject(result, ExternalUserInfoDTO.class);
    }

    @Override
    public String contactMeQrcode(String memberCode,String contactUid) {
        return contactSubAccountQrcode(memberCode, contactUid);
    }

    @Override
    public String contactMeQrcode(ChannelTypeEnum channelType, String dedicatedLinkCode) {
        //todo 如果后续切换到获客链接二维码，逻辑都需要改，同时需要更新大量已经存在于数据库的数据
        final AddContactWayDTO addContactWayDTO = AddContactWayDTO.builder()
//        联系方式类型,1-单人, 2-多人
                .type(1)
//        场景，1-在小程序中联系，2-通过二维码联系
                .scene(2)
                .user(new String[]{redisService.getChannelContactUserWithMinimumCount()})
                .state(ChannelTypeEnum.PREFIX + channelType.getLabel() + "-" + dedicatedLinkCode)
                .build();
        return requestForContactUrl(addContactWayDTO);
    }

    @Override
    public String contactMeQrcodeByState(String state, String contactUserId) {
        final AddContactWayDTO addContactWayDTO = AddContactWayDTO.builder()
//        联系方式类型,1-单人, 2-多人
                .type(1)
//        场景，1-在小程序中联系，2-通过二维码联系
                .scene(2)
                .user(new String[]{contactUserId})
                .state(state)
                .build();
        return requestForContactUrl(addContactWayDTO);
    }

    @Override
    public String contactSubAccountQrcode(String memberCode, String connectUser) {
        final AddContactWayDTO addContactWayDTO = AddContactWayDTO.builder()
//        联系方式类型,1-单人, 2-多人
                .type(1)
//        场景，1-在小程序中联系，2-通过二维码联系
                .scene(2)
                .user(new String[]{connectUser})
                .state(ChannelTypeEnum.PREFIX + ChannelTypeEnum.BUSINESS.getLabel() + "-" + memberCode)
                .build();
        return requestForContactUrl(addContactWayDTO);
    }

    private String requestForContactUrl(AddContactWayDTO addContactWayDTO) {
        return createQrcode(addContactWayDTO).getQrCode();
    }

    private AddContactWayResponseDTO createQrcode(AddContactWayDTO addContactWayDTO) {
        final String workWxAccessToken = getWorkWxAccessToken();
        final Request request = new Request.Builder()
                .url(String.format(WxConstant.ADD_CONTACT_WAY, workWxAccessToken))
                .post(RequestBody.create(JSON.toJSONString(addContactWayDTO), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("创建联系我二维码:{}", result);
        final AddContactWayResponseDTO addContactWayResponseDTO = JSON.parseObject(result, AddContactWayResponseDTO.class);
        weChatContactConfigService.save(WeChatContactConfig.builder()
                .configId(addContactWayResponseDTO.getConfigId())
                .qrCode(addContactWayResponseDTO.getQrCode())
                .build());
        return addContactWayResponseDTO;
    }

    @Override
    public void sendWelcomeMsg(WeWelcomeMsg weWelcomeMsg) {
        final String workWxAccessToken = getWorkWxAccessToken();

        final Request request = new Request.Builder()
                .url(String.format(WxConstant.SEND_WELCOME_MSG, workWxAccessToken))
                .post(RequestBody.create(JSON.toJSONString(weWelcomeMsg), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("客户发送欢迎语:{}", result);
    }

    @Override
    public List<WeChatGroupTagDTO> tagList(List<String> tagIds, List<String> groupIds) {
        final String workWxAccessToken = getWorkWxAccessToken();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tag_id", tagIds);
        jsonObject.put("group_id", groupIds);
        final Request request = new Request.Builder()
                .url(String.format(WxConstant.GET_TAG_LIST, workWxAccessToken))
                .post(RequestBody.create(jsonObject.toJSONString(), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("标签列表:{}", result);
        JSONObject object = JSONObject.parseObject(result);
        checkResult(object);
        return JSONObject.parseArray(JSONObject.toJSONString(object.get("tag_group")), WeChatGroupTagDTO.class);
    }

    @Override
    public String addTag(ChannelTypeEnum channelType, String tagName) {
        final String workWxAccessToken = getWorkWxAccessToken();
        final WeChatGroupTagDTO weChatGroupTagDTO = WeChatGroupTagDTO.builder()
                .group_id(channelType == ChannelTypeEnum.MARKETING ? workWeChatConfig.getMarketing() : workWeChatConfig.getDistribution())
                .tag(Arrays.asList(WeCropTagDTO.builder().name(channelType.getTagLabel() + "-" + tagName).build()))
                .build();

        final Request request = new Request.Builder()
                .url(String.format(WxConstant.ADD_GROUP_TAG, workWxAccessToken))
                .post(RequestBody.create(JSON.toJSONString(weChatGroupTagDTO), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("添加分组标签:{}", result);

        final WeChatAddTag weChatAddTag = JSON.parseObject(result, WeChatAddTag.class);

        if (!weChatAddTag.getErrcode().equals("0")) {
            log.debug("添加标签失败：{}", weChatAddTag.getErrmsg());
            if (weChatAddTag.getErrcode().equals("40071")) {
                throw new ServiceException("渠道名称已存在，无法使用~");
            }
            throw new ServiceException("系统错误，无法添加，请稍后重试！");
        }
        return weChatAddTag.getTag_group().getTag().get(0).getId();
    }

    @Override
    public void editTag(ChannelTypeEnum channelType, String tagId, String tagName) {
        final String workWxAccessToken = getWorkWxAccessToken();
        WeCropTagDTO weCropTagDTO = WeCropTagDTO.builder()
                .id(tagId)
                .name(channelType.getTagLabel() + "-" + tagName)
                .build();

        final Request request = new Request.Builder()
                .url(String.format(WxConstant.EDIT_GROUP_TAG, workWxAccessToken))
                .post(RequestBody.create(JSON.toJSONString(weCropTagDTO), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("修改分组标签:{}", result);

        JSONObject jsonObject = JSONObject.parseObject(result);
        checkResult(jsonObject);

    }

    @Override
    public void markTag(CustomerTagEdit customerTagEdit) {
        final String workWxAccessToken = getWorkWxAccessToken();
        final Request request = new Request.Builder()
                .url(String.format(WxConstant.MARK_TAG, workWxAccessToken))
                .post(RequestBody.create(JSON.toJSONString(customerTagEdit), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("客户标签编辑:{}", result);

        JSONObject jsonObject = JSONObject.parseObject(result);
        checkResult(jsonObject);
    }

    @Override
    public void changeExternalContactRemark(String userId, String externalUserId, String remark) {
        final String workWxAccessToken = getWorkWxAccessToken();
        final Request request = new Request.Builder()
                .url(String.format(WxConstant.CHANGE_EXTERNAL_CONTACT_REMARK, workWxAccessToken))
                .post(RequestBody.create(JSON.toJSONString(WeChatChangeRemarkDTO.builder()
                        .external_userid(externalUserId)
                        .userid(userId)
                        .remark("")
                        .remark_company(remark)
                        .build()), CONTENT_TYPE_JSON))
                .build();
        final String result = requestServer(okHttpClient.newCall(request));
        log.debug("客户备注编辑:{}", JSON.toJSONString(WeChatChangeRemarkDTO.builder()
                .external_userid(externalUserId)
                .userid(userId)
                .remark("")
                .remark_company(remark)
                .build()));
        log.debug("客户备注编辑:{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        checkResult(jsonObject);
    }


    private void checkResult(JSONObject jsonObject) {
        String errcode = Convert.toStr(jsonObject.get("errcode"));
        if (!errcode.equals("0")) {
            throw new ServiceException(jsonObject.get("errmsg").toString());
        }
    }

    private String requestServer(Call call) {
        try (Response response = call.execute()) {
            if (response.body() != null && response.isSuccessful()) {
                return response.body().string().replace("\n", "");
            }
        } catch (IOException e) {
            log.warn("请求接口异常:{}", e.getMessage());
        }
        throw new ServiceException("企业微信异常，请稍后再试~");
    }


}
