package com.wnkx.biz.wechat.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.system.api.domain.dto.biz.wechat.ContactUserCountDTO;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企业微信外部联系人信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Mapper
public interface WeChatExternalUserMapper extends SuperMapper<WeChatExternalUser> {

    /**
     * 通过externalUserid和unionid查询
     *
     * @return WeChatExternalUser
     */
    default WeChatExternalUser getOneByExternalUseridAndUnionid(String externalUserid, String unionid) {
        return this.selectOne(new LambdaQueryWrapper<WeChatExternalUser>()
                .eq(WeChatExternalUser::getExternalUserid, externalUserid)
                .eq(WeChatExternalUser::getUnionid, unionid)
                .last("limit 1")
        );
    }

    /**
     * 通过externalUserid将WeChatExternalUser设置为禁用
     */
    default void disableWeChatExternalUser(String externalUserid) {
        this.update(null, new LambdaUpdateWrapper<WeChatExternalUser>()
                .eq(WeChatExternalUser::getExternalUserid, externalUserid)
                .set(WeChatExternalUser::getStatus, StatusEnum.UN_ENABLED.getCode())
        );
    }

    /**
     * 通过unionid查询WeChatExternalUser
     */
    default WeChatExternalUser getOneByUnionid(String unionid) {
        return this.selectOne(new LambdaQueryWrapper<WeChatExternalUser>()
                .eq(WeChatExternalUser::getUnionid, unionid)
                .eq(WeChatExternalUser::getStatus, StatusEnum.ENABLED.getCode())
                .last("limit 1")
        );
    }

    List<ContactUserCountDTO> todayContactUser();

    default boolean hasRecordUser(String externalUserid) {
        return exists(new LambdaQueryWrapper<WeChatExternalUser>()
                .eq(WeChatExternalUser::getExternalUserid, externalUserid)
        );
    }

    default void updateContactUserId(String externalUserId, String connectUserId) {
        update(null, new LambdaUpdateWrapper<WeChatExternalUser>()
                .set(WeChatExternalUser::getConnectUserId, connectUserId)
                .eq(WeChatExternalUser::getExternalUserid, externalUserId)
        );
    }

    default void updateContactUserInfo(String externalUserId, String connectUserId, String contractUserName){
        update(null, new LambdaUpdateWrapper<WeChatExternalUser>()
                .set(WeChatExternalUser::getConnectUserId, connectUserId)
                .set(WeChatExternalUser::getConnectUserName, contractUserName)
                .eq(WeChatExternalUser::getExternalUserid, externalUserId)
        );
    }
}
