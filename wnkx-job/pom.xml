<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wnkx</groupId>
    <artifactId>wnkx-job</artifactId>
    <version>2.4.1</version>
    <packaging>pom</packaging>

    <name>wnkx-job</name>
    <description>蜗牛开箱-分布式任务</description>
    <url>https://www.xuxueli.com/</url>

    <modules>
        <module>xxl-job-core</module>
        <module>xxl-job-admin</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>

        <netty.version>4.1.108.Final</netty.version>
        <gson.version>2.10.1</gson.version>

        <spring.version>5.3.34</spring.version>
        <spring-boot.version>2.7.18</spring-boot.version>

        <mybatis-spring-boot-starter.version>2.3.2</mybatis-spring-boot-starter.version>
        <mysql-connector-j.version>8.3.0</mysql-connector-j.version>

        <slf4j-api.version>2.0.13</slf4j-api.version>
        <junit-jupiter.version>5.10.2</junit-jupiter.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>

        <groovy.version>4.0.21</groovy.version>

        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.6.3</maven-javadoc-plugin.version>
        <maven-gpg-plugin.version>3.2.3</maven-gpg-plugin.version>
        <xxl-job.version>2.4.1</xxl-job.version>
    </properties>

    <licenses>
        <license>
            <name>GNU General Public License version 3</name>
            <url>https://opensource.org/licenses/GPL-3.0</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>XXL</id>
            <name>xuxueli</name>
            <email><EMAIL></email>
            <url>https://github.com/xuxueli</url>
        </developer>
    </developers>

    <profiles>

        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <!-- Source -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
