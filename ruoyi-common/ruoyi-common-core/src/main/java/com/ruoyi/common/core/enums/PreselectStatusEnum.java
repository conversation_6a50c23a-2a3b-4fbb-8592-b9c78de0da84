package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预选模特状态枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PreselectStatusEnum {

    UN_JOINTED(0, "未对接"),
    JOINTED(1, "已对接"),
    SELECTED(2, "已选定"),
    OUT(3, "已淘汰"),
    ;
    private Integer code;
    private String label;

    public static List<Integer> getCodes() {
        return Arrays.stream(PreselectStatusEnum.values()).map(PreselectStatusEnum::getCode).collect(Collectors.toList());
    }
}
