package com.ruoyi.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 微信回调事件枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum WxCallBackEventEnum {

    CHANGE_EXTERNAL_CONTACT(0, "change_external_contact", "更新外部联系人"),
    ADD_HALF_EXTERNAL_CONTACT(1, "add_half_external_contact", "添加企业客户事件"),
    ADD_EXTERNAL_CONTACT(2, "add_external_contact", "外部联系人免验证添加成员事件"),
    DEL_EXTERNAL_CONTACT(3, "del_external_contact", "删除企业客户事件(企业微信用户主动删除)"),
    DEL_FOLLOW_USER(4, "del_follow_user", "删除企业客户事件(企业微信用户被删除)"),
    UNKNOWN(5, "unknown", "未知事件");


    private Integer code;
    private String value;
    private String desc;


    public static String getDesc(Integer code) {
        for (WxCallBackEventEnum e : WxCallBackEventEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return UNKNOWN.getDesc();
    }

    public static String getValue(Integer code) {
        for (WxCallBackEventEnum e : WxCallBackEventEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getValue();
            }
        }
        return UNKNOWN.getValue();
    }

    public static WxCallBackEventEnum getByCode(Integer code) {
        for (WxCallBackEventEnum e : WxCallBackEventEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return UNKNOWN;
    }
}
